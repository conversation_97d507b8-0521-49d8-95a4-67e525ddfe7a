---
type: "always_apply"
---

# GymBro 代码实现核心规范

## 🎯 核心原则

### 严格禁止代码捏造
- **强制要求**: 所有代码必须基于 [code-examples](mdc:code-examples) 和现有文件模式
- **禁止虚构**: 任何API、接口或方法的虚构都是严格禁止的
- **参考优先级**:
  1. MUST: [code-examples/README.md](mdc:code-examples/README.md) 中的对应示例
  2. SECONDARY: 同类型现有文件的实现模式
  3. FORBIDDEN: 任何形式的代码捏造或虚构API

### 100%功能完整性标准
- **实现流程**:
  1. **功能实装(100%完整)**: 所有核心功能必须完整实现，无占位符/TODO
  2. **错误处理实施**: 使用Result<T>包装所有可能失败的操作
  3. **Timber日志写入**: 仅在关键核心位置写入一次
- **零容忍政策**: 禁止任何TODO、FIXME或占位符

## 🏗️ MVI架构强制要求

### MVI 2.0 核心模式
- **始终继承 BaseMviViewModel** - 这是不可协商的要求
- **Contract对象**: 每个功能模块必须定义公共Contract对象作为API
- **State规则**: 必须是@Immutable data class，所有属性为'val'，确保Compose性能
- **单一StateFlow**: 每个ViewModel只能有一个StateFlow<State>
- **Intent命名**: 使用动词/gerund（LoadWorkout, StartTimer），Result intent必须以'Result'结尾
- **Effect命名**: 使用动词表示命令（NavigateToHome, ShowErrorToast）
- **纯Reducer**: 必须是纯函数，无副作用，仅进行状态转换
- **EffectHandler**: 唯一允许执行副作用的组件（网络、数据库、I/O）

### 参考模式强制要求
必须参考这些关键示例：
- [mvi_basic_implementation.kt](mdc:code-examples/mvi_basic_implementation.kt) - MVI结构标准
- [repository_implementation.kt](mdc:code-examples/repository_implementation.kt) - 数据层模式
- [usecase_patterns.kt](mdc:code-examples/usecase_patterns.kt) - 业务逻辑模式
- [contract_standards.kt](mdc:code-examples/contract_standards.kt) - 契约定义标准

## 📐 严格尺寸约束

### 硬性限制
- **函数限制**: ≤80行，单一职责
- **文件限制**: ≤500行，逻辑内聚
- **包结构**: 必须使用子包内聚，**严禁根目录文件**

### 架构约束强制执行
- **依赖流**: Features → Domain → Data → Core（严格单向）
- **Hilt DI**: 所有依赖通过接口注入，不注入具体实现
- **Result<T>包装**: 所有数据/domain层可能失败的操作必须使用
- **设计系统**: 仅使用Tokens.*，严禁硬编码值

## 🚫 绝对禁止事项

### 代码质量禁令
- ❌ **代码捏造**: 任何形式的API或接口虚构
- ❌ **功能越界**: 创建task范围外的额外功能（需用户审核）
- ❌ **占位内容**: TODO、FIXME、或任何未完成标记
- ❌ **尺寸违规**: 超过函数80行或文件500行限制
- ❌ **结构违规**: 在根目录创建任何文件
- ❌ **标准违规**: 不符合MVI命名规范或项目约定
- ❌ **日志滥用**: 多次Timber日志写入（每文件最多一次）

### 架构禁令
- ❌ Features不能直接依赖其他features
- ❌ Domain不能依赖Android框架
- ❌ Data不能依赖presentation层
- ❌ Core模块不能依赖feature模块

## ✅ 质量保证检查点

### 实现前必须验证
1. ✅ 检查[code-examples](mdc:code-examples)目录中的相关模式
2. ✅ 分析现有相似功能的实现
3. ✅ 确认所有使用的接口和工具都在代码库中存在
4. ✅ 验证是否遵循既定的架构模式

### 实现后必须通过
1. ✅ 100%功能完整性验证（无占位符）
2. ✅ 编译通过，无警告无错误
3. ✅ 遵循[code-examples](mdc:code-examples)模式
4. ✅ 正确的error handling和state管理
5. ✅ 使用设计系统tokens，无硬编码值
6. ✅ Hilt依赖注入正确配置
7. ✅ 包结构合规（子包only）
8. ✅ 函数/文件尺寸约束满足

## 🔄 实现工作流

### 必须的实现步骤
1. **分析现有模式**: 在代码库中搜索相似实现
2. **创建完整MVI结构**: Contract, State, Intent, Effect, ViewModel, Reducer, EffectHandler
3. **确保UseCase集成**: 业务逻辑的正确分离
4. **实现错误处理**: 完整的Result<T>处理
5. **遵循命名约定**: 严格按照既定标准
6. **使用设计系统**: 一致的tokens使用

生成的代码必须在功能上完整、生产就绪，并与现有GymBro架构无缝集成，同时保持最高的代码质量和架构一致性标准。

6. Use the design system tokens consistently

You must generate functionally complete, production-ready Android code that seamlessly integrates with the existing GymBro architecture while maintaining the highest standards of code quality and architectural consistency.
