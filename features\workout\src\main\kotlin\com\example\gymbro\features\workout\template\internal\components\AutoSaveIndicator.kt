package com.example.gymbro.features.workout.template.internal.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.ErrorOutline
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.template.TemplateContract
import kotlinx.coroutines.delay

/**
 * 自动保存状态指示器 - P4阶段新增
 *
 * 🎯 功能:
 * - 显示自动保存状态 (Saving/Success/Failed)
 * - 平滑的状态转换动画
 * - 自动隐藏成功状态
 * - 错误状态的重试功能
 *
 * 🏗️ 架构原则:
 * - 使用 designSystem 主题令牌
 * - 流畅的动画体验
 * - 无障碍访问支持
 */
@Composable
fun AutoSaveIndicator(
    state: TemplateContract.AutoSaveState,
    errorMessage: String? = null,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    var isVisible by remember { mutableStateOf(false) }

    // 根据状态控制显示
    LaunchedEffect(state) {
        when (state) {
            TemplateContract.AutoSaveState.Inactive -> {
                isVisible = false
            }
            TemplateContract.AutoSaveState.Saving -> {
                isVisible = true
            }
            TemplateContract.AutoSaveState.Success -> {
                isVisible = true
                delay(2000) // 显示2秒后自动隐藏
                isVisible = false
            }
            TemplateContract.AutoSaveState.Failed -> {
                isVisible = true
            }
        }
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            animationSpec = tween(
                durationMillis = MotionDurations.CONTENT_TRANSITION,
                easing = MotionEasings.STANDARD,
            ),
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = MotionDurations.CONTENT_TRANSITION,
                easing = MotionEasings.STANDARD,
            ),
        ),
        exit = slideOutVertically(
            animationSpec = tween(
                durationMillis = MotionDurations.CONTENT_TRANSITION,
                easing = MotionEasings.STANDARD,
            ),
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = MotionDurations.CONTENT_TRANSITION,
                easing = MotionEasings.STANDARD,
            ),
        ),
        modifier = modifier,
    ) {
        AutoSaveIndicatorContent(
            state = state,
            errorMessage = errorMessage,
            onRetry = onRetry,
        )
    }
}

@Composable
private fun AutoSaveIndicatorContent(
    state: TemplateContract.AutoSaveState,
    errorMessage: String?,
    onRetry: (() -> Unit)?,
) {
    val (backgroundColor, contentColor, icon, text) = when (state) {
        TemplateContract.AutoSaveState.Inactive -> return
        TemplateContract.AutoSaveState.Saving -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.9f),
            contentColor = MaterialTheme.colorScheme.onPrimary,
            icon = Icons.Default.CloudUpload,
            text = "保存中...",
        )
        TemplateContract.AutoSaveState.Success -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.completedState.copy(alpha = 0.9f),
            contentColor = MaterialTheme.colorScheme.onPrimary,
            icon = Icons.Default.CheckCircle,
            text = "已保存",
        )
        TemplateContract.AutoSaveState.Failed -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.errorPrimary.copy(alpha = 0.9f),
            contentColor = MaterialTheme.workoutColors.textPrimary, // 🔥 Phase 0: 使用 workoutColors 替代 colorScheme
            icon = Icons.Default.ErrorOutline,
            text = errorMessage ?: "保存失败",
        )
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Tokens.Spacing.Medium),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = Tokens.Elevation.Small,
        ),
        shape = RoundedCornerShape(Tokens.Radius.Card),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 图标
            if (state == TemplateContract.AutoSaveState.Saving) {
                // 旋转的保存图标
                val infiniteTransition = rememberInfiniteTransition(label = "saving")
                val rotation by infiniteTransition.animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart,
                    ),
                    label = "rotation",
                )

                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier
                        .size(Tokens.Icon.Medium)
                        .graphicsLayer { rotationZ = rotation },
                )
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier.size(Tokens.Icon.Medium),
                )
            }

            // 文本
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                color = contentColor,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f),
            )

            // 重试按钮（仅在失败时显示）
            if (state == TemplateContract.AutoSaveState.Failed && onRetry != null) {
                TextButton(
                    onClick = onRetry,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = contentColor,
                    ),
                    contentPadding = PaddingValues(
                        horizontal = Tokens.Spacing.Small,
                        vertical = Tokens.Spacing.Tiny,
                    ),
                ) {
                    Text(
                        text = "重试",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold,
                    )
                }
            }
        }
    }
}

/**
 * 自动保存样式数据类
 */
private data class AutoSaveStyle(
    val backgroundColor: Color,
    val contentColor: Color,
    val icon: ImageVector,
    val text: String,
)

/**
 * 浮动自动保存指示器（用于全屏编辑界面）
 */
@Composable
fun FloatingAutoSaveIndicator(
    state: TemplateContract.AutoSaveState,
    errorMessage: String? = null,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(state) {
        when (state) {
            TemplateContract.AutoSaveState.Inactive -> {
                isVisible = false
            }
            TemplateContract.AutoSaveState.Saving -> {
                isVisible = true
            }
            TemplateContract.AutoSaveState.Success -> {
                isVisible = true
                delay(1500) // 浮动版本显示时间更短
                isVisible = false
            }
            TemplateContract.AutoSaveState.Failed -> {
                isVisible = true
            }
        }
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = scaleIn(
            animationSpec = tween(
                durationMillis = MotionDurations.Component.CARD_ENTER,
                easing = MotionEasings.STANDARD,
            ),
            initialScale = 0.95f,
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = MotionDurations.Component.CARD_ENTER,
                easing = MotionEasings.STANDARD,
            ),
        ),
        exit = scaleOut(
            animationSpec = tween(
                durationMillis = MotionDurations.Component.PAGE_EXIT,
                easing = MotionEasings.STANDARD,
            ),
            targetScale = 0.95f,
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = MotionDurations.Component.PAGE_EXIT,
                easing = MotionEasings.STANDARD,
            ),
        ),
        modifier = modifier,
    ) {
        FloatingAutoSaveIndicatorContent(
            state = state,
            errorMessage = errorMessage,
            onRetry = onRetry,
        )
    }
}

@Composable
private fun FloatingAutoSaveIndicatorContent(
    state: TemplateContract.AutoSaveState,
    errorMessage: String?,
    onRetry: (() -> Unit)?,
) {
    val (backgroundColor, contentColor, icon, text) = when (state) {
        TemplateContract.AutoSaveState.Inactive -> return
        TemplateContract.AutoSaveState.Saving -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.95f),
            contentColor = MaterialTheme.workoutColors.accentPrimary,
            icon = Icons.Default.CloudUpload,
            text = "保存中",
        )
        TemplateContract.AutoSaveState.Success -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.95f),
            contentColor = Tokens.Color.Success,
            icon = Icons.Default.CheckCircle,
            text = "已保存",
        )
        TemplateContract.AutoSaveState.Failed -> AutoSaveStyle(
            backgroundColor = MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.95f),
            contentColor = Tokens.Color.Error,
            icon = Icons.Default.ErrorOutline,
            text = "保存失败",
        )
    }

    Surface(
        modifier = Modifier
            .clip(RoundedCornerShape(Tokens.Radius.Large))
            .background(backgroundColor),
        color = backgroundColor,
        shadowElevation = Tokens.Elevation.Medium,
    ) {
        Row(
            modifier = Modifier.padding(
                horizontal = Tokens.Spacing.Medium,
                vertical = Tokens.Spacing.Small,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 图标
            if (state == TemplateContract.AutoSaveState.Saving) {
                val infiniteTransition = rememberInfiniteTransition(label = "floating_saving")
                val rotation by infiniteTransition.animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart,
                    ),
                    label = "floating_rotation",
                )

                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier
                        .size(Tokens.Icon.Small)
                        .graphicsLayer { rotationZ = rotation },
                )
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier.size(Tokens.Icon.Small),
                )
            }

            // 文本
            Text(
                text = text,
                style = MaterialTheme.typography.labelMedium,
                color = contentColor,
                fontWeight = FontWeight.Medium,
            )

            // 重试按钮（仅在失败时显示）
            if (state == TemplateContract.AutoSaveState.Failed && onRetry != null) {
                IconButton(
                    onClick = onRetry,
                    modifier = Modifier.size(Tokens.Icon.Large),
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "重试",
                        tint = contentColor,
                        modifier = Modifier.size(Tokens.Icon.XSmall),
                    )
                }
            }
        }
    }
}
