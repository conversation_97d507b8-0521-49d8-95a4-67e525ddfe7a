19:54:59.409                          D  ConversationPagingSource
19:54:59.410                          D  ConversationPagingSource
19:54:59.410                          D  ConversationPagingSource
19:54:59.411                          D  ConversationPagingSource
19:54:59.412                          D  ConversationPagingSource
19:54:59.419                          D  ConversationPagingSource
19:54:59.419                          D  ConversationPagingSource
19:55:23.402 TB                       D  🔥 AiCoachSessionHandler: 🔥 [消息保存修复] 开始保存用户消息: sessionId=7b8a***-***-****e0-ada5-***-***-****e-8fc6-de***-***-****e***-***-****ba2b, messageId=***-***-****dc***-***-****-a3b8-***-***-****c-***-***-****d-c***-***-****fbd***-***-****d
System.out               I  🔥 PrepareAiContextUseCase: profileResult类型 = Success
19:55:23.531                          I  🔥 profileResult.Success: data = UserProfile(userId=d77cfb45-2d1f-43e2-ad3a-d70bf063243c, username=null, displayName=null, email=null, phoneNumber=null, avatarUrl=null, bio=null, gender=OTHER, height=null, heightUnit=CM, weight=null, weightUnit=KG, fitnessLevel=BEGINNER, fitnessGoals=[], joinDate=2025-07-25T17:29:59.878, lastActive=2025-07-25T17:29:59.878, workoutDays=[], allowPartnerMatching=false, totalActivityCount=0, weeklyActiveMinutes=0, likesReceived=0, isSubscribed=false, isAnonymous=false, socialLinks={}, isMpOwner=false)
19:55:23.531                          I  🔥 [诊断3] 构建的AiUserProfile:
19:55:23.531                          I  🔥 [诊断3] - gender: 其他
19:55:23.531                          I  🔥 [诊断3] - age: 25
19:55:23.531                          I  🔥 [诊断3] - height: 170
19:55:23.531                          I  🔥 [诊断3] - weight: 65
19:55:23.531                          I  🔥 [诊断3] - experience: 初学者
19:55:23.531                          I  🔥 [诊断3] - bodyFatPercentage: null
19:55:23.531                          I  🔥 [诊断3] ✅ 使用了真实的用户数据
19:55:23.531                          I  🔥 PrepareAiContextUseCase: 用户资料数据获取结果
19:55:23.531                          I  🔥 userProfileData: UserProfile(userId=d77cfb45-2d1f-43e2-ad3a-d70bf063243c, username=null, displayName=null, email=null, phoneNumber=null, avatarUrl=null, bio=null, gender=OTHER, height=null, heightUnit=CM, weight=null, weightUnit=KG, fitnessLevel=BEGINNER, fitnessGoals=[], joinDate=2025-07-25T17:29:59.878, lastActive=2025-07-25T17:29:59.878, workoutDays=[], allowPartnerMatching=false, totalActivityCount=0, weeklyActiveMinutes=0, likesReceived=0, isSubscribed=false, isAnonymous=false, socialLinks={}, isMpOwner=false)
19:55:23.531                          I  🔥 用户数据详情:
19:55:23.531                          I    - userId: d77cfb45-2d1f-43e2-ad3a-d70bf063243c
19:55:23.531                          I    - displayName: null
19:55:23.531                          I    - email: null
19:55:23.531                          I    - gender: OTHER
19:55:23.531                          I    - height: null
19:55:23.531                          I    - weight: null
19:55:23.531                          I    - fitnessLevel: BEGINNER
19:55:23.531                          I    - fitnessGoals: []
19:55:23.531                          I  🔥 构建的AiUserProfile: AiUserProfile(gender=其他, age=25, height=170, weight=65, experience=初学者, bodyFatPercentage=null)
19:55:23.539 TB                       D  聊天会话加载成功: title=我想开始力量训练，我应该从哪里..., messageCount=1
19:55:23.539                          D  🔥 AiCoachSessionHandler: 会话已有自定义标题，跳过生成: 我想开始力量训练，我应该从哪里...
19:55:23.547                          D  🎯 [AiCoachViewModel] Processing: CheckProfileCompletion
19:55:23.548                          D  MemoryContextBuilder: 开始构建上下文，用户输入长度=***-***-****
19:55:23.549                          D  MemoryContextBuilder: 上下文构建完成，类型=TRAINING，查询=我想开始力量训练，我应该从哪里开始？ 请了解我的基本信息和相关训练背景
19:55:23.550                          I  🧹 强制清除standard缓存，重新加载最新版本
19:55:23.553                          E  🔍 [DEBUG] 文件对比结果:
19:55:23.554                          E  📁 Assets版本: GymBro Fitness Coach · ThinkingML v4.5 (strict tag regime)
19:55:23.555                          E  💾 本地版本: GymBro Fitness Coach · ThinkingML v4.5 (strict tag regime)
19:55:23.555                          E  📊 Assets哈希: ***-***-****
19:55:23.555                          E  📊 本地哈希: ***-***-****
19:55:23.556                          E  📏 Assets长度: ***-***-****
19:55:23.556                          E  📏 本地长度: ***-***-****
19:55:23.556                          E  ✅ [DEBUG] 文件内容一致
19:55:23.558                          D  🔍 检查配置版本一致性: standard
19:55:23.558                          D  ✅ standard.json版本一致，无需更新
19:55:23.559                          D  🔍 尝试加载配置: standard，路径: /data/user/0/com.example.gymbro/files/prompts/standard.json
