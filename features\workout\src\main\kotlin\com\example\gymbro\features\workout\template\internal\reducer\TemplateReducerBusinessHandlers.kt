package com.example.gymbro.features.workout.template.internal.reducer

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.shared.models.workout.Difficulty
import com.example.gymbro.shared.models.workout.TemplateCategory
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.TemplateSource
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 模板业务逻辑处理器 - P4阶段增强版
 *
 * 🎯 P4阶段新增功能:
 * - 搜索和筛选逻辑优化
 * - 自动保存状态管理
 * - 缓存恢复机制
 * - 错误处理增强
 *
 * 负责处理核心业务逻辑：
 * - 模板管理 (CRUD)
 * - 草稿管理 (CRUD)
 * - 动作管理 (添加/删除/更新)
 * - 搜索和筛选 (P4增强)
 * - 缓存管理 (P4增强)
 */
@Singleton
internal class TemplateReducerBusinessHandlers @Inject constructor() {

    // === 模板管理处理函数 ===

    fun handleLoadTemplates(intent: TemplateContract.Intent.LoadTemplates, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isLoading = true,
                error = null,
            ),
            effect = TemplateContract.Effect.LoadTemplatesData, // 🔥 修复：生成Effect触发数据加载
        )
    }

    fun handleRefreshTemplates(intent: TemplateContract.Intent.RefreshTemplates, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isLoading = true,
                error = null,
            ),
            effect = TemplateContract.Effect.RefreshTemplatesData, // 🔥 修复：生成Effect触发数据刷新
        )
    }

    fun handleCreateTemplate(intent: TemplateContract.Intent.CreateTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val newTemplate = WorkoutTemplateDto(
            id = UUID.randomUUID().toString(),
            name = intent.name,
            description = "",
            exercises = emptyList(),
            difficulty = Difficulty.MEDIUM,
            category = TemplateCategory.STRENGTH,
            source = TemplateSource.USER,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            version = 1,
        )

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                currentTemplate = newTemplate,
                editingTemplate = newTemplate,
                hasUnsavedChanges = true,
            ),
            effect = TemplateContract.Effect.NavigateToEditTemplate(newTemplate.id),
        )
    }

    fun handleEditTemplate(intent: TemplateContract.Intent.EditTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val template = state.templates.find { it.id == intent.templateId }
        return if (template != null) {
            TemplateReducer.ReduceResult.stateAndEffect(
                newState = state.copy(
                    currentTemplate = template,
                    editingTemplate = template.copy(),
                    hasUnsavedChanges = false,
                ),
                effect = TemplateContract.Effect.NavigateToEditTemplate(intent.templateId),
            )
        } else {
            TemplateReducer.ReduceResult.stateAndEffect(
                newState = state.copy(
                    error = UiText.DynamicString("模板未找到"),
                ),
                effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("模板未找到")),
            )
        }
    }

    // 🔥 修复：移除主界面的保存逻辑处理器
    // 这些方法已被移除，因为主界面不应该执行模板保存操作
    // 所有保存逻辑都应该在 TemplateEditScreen 中处理

    fun handleDeleteTemplate(intent: TemplateContract.Intent.DeleteTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isDeleting = true,
                deleteTargetId = intent.templateId,
            ),
            effect = TemplateContract.Effect.DeleteTemplateData(intent.templateId), // 🔥 修复：生成Effect触发删除
        )
    }

    fun handleDuplicateTemplate(intent: TemplateContract.Intent.DuplicateTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val originalTemplate = state.templates.find { it.id == intent.templateId }
        return if (originalTemplate != null) {
            val duplicatedTemplate = originalTemplate.copy(
                id = UUID.randomUUID().toString(),
                name = "${originalTemplate.name} (副本)",
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
            )

            TemplateReducer.ReduceResult.stateOnly(
                state.copy(
                    templates = state.templates + duplicatedTemplate,
                    filteredTemplates = if (state.searchQuery.isBlank()) {
                        state.templates + duplicatedTemplate
                    } else {
                        filterTemplatesByQuery(state.templates + duplicatedTemplate, state.searchQuery)
                    },
                ),
            )
        } else {
            TemplateReducer.ReduceResult.stateAndEffect(
                newState = state.copy(
                    error = UiText.DynamicString("要复制的模板未找到"),
                ),
                effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("要复制的模板未找到")),
            )
        }
    }

    // === 搜索和筛选处理函数 (P4增强) ===

    fun handleSearchTemplates(intent: TemplateContract.Intent.SearchTemplates, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 🔥 修复：应用用户自定义排序后再进行搜索筛选
        val sortedTemplates = applySortOrder(state.templates, state.templateOrder)
        val sortedDrafts = applySortOrder(state.drafts, state.draftOrder)

        val filteredTemplates = filterTemplatesByQuery(sortedTemplates, intent.query)
        val filteredDrafts = filterDraftsByQuery(sortedDrafts, intent.query)

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                searchQuery = intent.query,
                filteredTemplates = filteredTemplates,
                filteredDrafts = filteredDrafts,
                isSearching = intent.query.isNotBlank(),
            ),
        )
    }

    fun handleFilterByCategory(intent: TemplateContract.Intent.FilterByCategory, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 🔥 修复：应用用户自定义排序后再进行分类筛选
        val sortedTemplates = applySortOrder(state.templates, state.templateOrder)

        val filteredTemplates = if (intent.category == TemplateContract.Categories.ALL) {
            sortedTemplates
        } else {
            sortedTemplates.filter { template ->
                template.category.name.equals(intent.category, ignoreCase = true)
            }
        }

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                selectedCategory = intent.category,
                filteredTemplates = filteredTemplates,
            ),
        )
    }

    fun handleClearFilters(intent: TemplateContract.Intent.ClearFilters, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                searchQuery = "",
                selectedCategory = "",
                filteredTemplates = state.templates,
                filteredDrafts = state.drafts,
                isSearching = false,
            ),
        )
    }

    // === P4新增：搜索切换处理函数 ===

    fun handleToggleSearch(intent: TemplateContract.Intent.ToggleSearch, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                showSearchField = !state.showSearchField,
                searchQuery = if (!state.showSearchField) state.searchQuery else "",
                isSearching = if (!state.showSearchField) state.isSearching else false,
            ),
        )
    }

    fun handleShowSearch(intent: TemplateContract.Intent.ShowSearch, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showSearchField = true),
        )
    }

    fun handleHideSearch(intent: TemplateContract.Intent.HideSearch, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 🔥 修复：隐藏搜索时应用用户自定义排序
        val sortedTemplates = applySortOrder(state.templates, state.templateOrder)
        val sortedDrafts = applySortOrder(state.drafts, state.draftOrder)

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                showSearchField = false,
                searchQuery = "",
                isSearching = false,
                filteredTemplates = sortedTemplates,
                filteredDrafts = sortedDrafts,
            ),
        )
    }

    // === 草稿管理处理函数 ===

    fun handleLoadDrafts(intent: TemplateContract.Intent.LoadDrafts, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isLoadingDrafts = true,
                error = null,
            ),
            effect = TemplateContract.Effect.LoadDraftsData, // 🔥 修复：生成Effect触发草稿加载
        )
    }

    fun handleRefreshDrafts(intent: TemplateContract.Intent.RefreshDrafts, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isLoadingDrafts = true,
                error = null,
            ),
            effect = TemplateContract.Effect.RefreshDraftsData, // 🔥 修复：生成Effect触发草稿刷新
        )
    }

    fun handleCreateNewDraft(intent: TemplateContract.Intent.CreateNewDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state,
            effect = TemplateContract.Effect.NavigateToCreateDraft,
        )
    }

    fun handleEditDraft(intent: TemplateContract.Intent.EditDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state,
            effect = TemplateContract.Effect.NavigateToDraftEditor(intent.draftId),
        )
    }

    fun handleSaveDraft(intent: TemplateContract.Intent.SaveDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                isSaving = true,
                saveError = null,
            ),
        )
    }

    fun handleDeleteDraft(intent: TemplateContract.Intent.DeleteDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isDeleting = true,
                deleteTargetId = intent.draftId,
            ),
            effect = TemplateContract.Effect.DeleteDraftData(intent.draftId), // 🔥 修复：生成Effect触发草稿删除
        )
    }

    fun handlePromoteDraft(intent: TemplateContract.Intent.PromoteDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val draft = state.drafts.find { it.id == intent.draftId }
        return if (draft != null) {
            TemplateReducer.ReduceResult.stateAndEffect(
                newState = state.copy(isSaving = true),
                effect = TemplateContract.Effect.ShowPromoteDraftDialog(draft.name),
            )
        } else {
            TemplateReducer.ReduceResult.stateAndEffect(
                newState = state.copy(
                    error = UiText.DynamicString("草稿未找到"),
                ),
                effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("草稿未找到")),
            )
        }
    }

    // === 动作管理处理函数 ===

    fun handleShowExerciseSelector(intent: TemplateContract.Intent.ShowExerciseSelector, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(showExerciseSelector = true),
            effect = TemplateContract.Effect.NavigateToExerciseSelector(),
        )
    }

    fun handleHideExerciseSelector(intent: TemplateContract.Intent.HideExerciseSelector, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showExerciseSelector = false),
        )
    }

    fun handleSetExerciseSelectorMode(intent: TemplateContract.Intent.SetExerciseSelectorMode, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(exerciseSelectorMode = intent.mode),
        )
    }

    fun handleAddExercise(intent: TemplateContract.Intent.AddExercise, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplate = state.editingTemplate ?: return TemplateReducer.ReduceResult.stateOnly(state)

        // 🔥 关键修复：为新添加的动作生成完整的 customSets 数据结构
        val defaultCustomSets = (1..3).map { setNumber ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = setNumber,
                targetWeight = 0f,
                targetReps = 10,
                restTimeSeconds = 90,
                targetDuration = null,
                rpe = null,
            )
        }

        val newExercise = TemplateExerciseDto(
            id = com.example.gymbro.shared.models.workout.WorkoutTemplateDto.generateId(),
            exerciseId = intent.exercise.id,
            exerciseName = when (val name = intent.exercise.name) {
                is String -> if (name.isNotBlank()) name else "未知动作"
                else -> "未知动作"
            },
            sets = 3,
            reps = 10,
            rpe = null,
            targetWeight = 0f, // 🔥 修复：使用默认值而不是 null
            restTimeSeconds = 90,
            customSets = defaultCustomSets, // 🔥 关键修复：添加默认的 customSets
        )

        val updatedTemplate = currentTemplate.copy(
            exercises = currentTemplate.exercises + newExercise,
            updatedAt = System.currentTimeMillis(),
        )

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                editingTemplate = updatedTemplate,
                hasUnsavedChanges = true,
                selectedExercises = state.selectedExercises + intent.exercise,
            ),
        )
    }

    fun handleAddExercises(intent: TemplateContract.Intent.AddExercises, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplate = state.editingTemplate ?: return TemplateReducer.ReduceResult.stateOnly(state)

        val newExercises = intent.exercises.map { exercise ->
            TemplateExerciseDto(
                exerciseId = exercise.id,
                sets = 3,
                reps = 10,
                rpe = null,
                targetWeight = null,
                restTimeSeconds = 90,
            )
        }

        val updatedTemplate = currentTemplate.copy(
            exercises = currentTemplate.exercises + newExercises,
            updatedAt = System.currentTimeMillis(),
        )

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                editingTemplate = updatedTemplate,
                hasUnsavedChanges = true,
                selectedExercises = state.selectedExercises + intent.exercises,
            ),
        )
    }

    fun handleRemoveExercise(intent: TemplateContract.Intent.RemoveExercise, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplate = state.editingTemplate ?: return TemplateReducer.ReduceResult.stateOnly(state)

        val updatedTemplate = currentTemplate.copy(
            exercises = currentTemplate.exercises.filter { it.exerciseId != intent.exerciseId },
            updatedAt = System.currentTimeMillis(),
        )

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                editingTemplate = updatedTemplate,
                hasUnsavedChanges = true,
            ),
        )
    }

    fun handleUpdateExercise(intent: TemplateContract.Intent.UpdateExercise, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplate = state.editingTemplate ?: return TemplateReducer.ReduceResult.stateOnly(state)

        val updatedTemplate = currentTemplate.copy(
            exercises = currentTemplate.exercises.map { exercise ->
                if (exercise.exerciseId == intent.exercise.exerciseId) {
                    intent.exercise
                } else {
                    exercise
                }
            },
            updatedAt = System.currentTimeMillis(),
        )

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                editingTemplate = updatedTemplate,
                hasUnsavedChanges = true,
            ),
        )
    }

    fun handleReorderExercises(intent: TemplateContract.Intent.ReorderExercises, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplate = state.editingTemplate ?: return TemplateReducer.ReduceResult.stateOnly(state)

        val exercises = currentTemplate.exercises.toMutableList()
        if (intent.fromIndex in exercises.indices && intent.toIndex in exercises.indices) {
            val movedExercise = exercises.removeAt(intent.fromIndex)
            exercises.add(intent.toIndex, movedExercise)

            val updatedTemplate = currentTemplate.copy(
                exercises = exercises,
                updatedAt = System.currentTimeMillis(),
            )

            return TemplateReducer.ReduceResult.stateOnly(
                state.copy(
                    editingTemplate = updatedTemplate,
                    hasUnsavedChanges = true,
                ),
            )
        }

        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    // === 自动保存管理 (P4增强) ===

    fun handleSaveToCache(intent: TemplateContract.Intent.SaveToCache, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                autoSaveState = TemplateContract.AutoSaveState.Saving,
            ),
        )
    }

    fun handleRestoreFromCache(intent: TemplateContract.Intent.RestoreFromCache, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                showCacheRestoreDialog = true,
            ),
        )
    }

    fun handleClearCache(intent: TemplateContract.Intent.ClearCache, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                cachedChanges = state.cachedChanges - intent.templateId,
                cacheAvailable = state.cachedChanges.size > 1,
            ),
        )
    }

    fun handleEnableAutoSave(intent: TemplateContract.Intent.EnableAutoSave, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(autoSaveEnabled = true),
        )
    }

    fun handleDisableAutoSave(intent: TemplateContract.Intent.DisableAutoSave, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(autoSaveEnabled = false),
        )
    }

    fun handleShowCacheRestoreDialog(intent: TemplateContract.Intent.ShowCacheRestoreDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showCacheRestoreDialog = true),
        )
    }

    fun handleHideCacheRestoreDialog(intent: TemplateContract.Intent.HideCacheRestoreDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showCacheRestoreDialog = false),
        )
    }

    fun handleRestoreSpecificCache(intent: TemplateContract.Intent.RestoreSpecificCache, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val cachedTemplate = state.cachedChanges[intent.templateId]
        return if (cachedTemplate != null) {
            TemplateReducer.ReduceResult.stateOnly(
                state.copy(
                    editingTemplate = cachedTemplate,
                    hasUnsavedChanges = true,
                    showCacheRestoreDialog = false,
                ),
            )
        } else {
            TemplateReducer.ReduceResult.stateOnly(state)
        }
    }

    // === 拖拽排序处理函数 ===

    fun handleStartDragTemplate(intent: TemplateContract.Intent.StartDragTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                isDragging = true,
                draggedItem = intent.templateId,
                draggedItemIndex = intent.currentIndex,
                dragTargetIndex = intent.currentIndex,
            ),
        )
    }

    fun handleUpdateDragTemplate(intent: TemplateContract.Intent.UpdateDragTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 获取当前过滤后的模板列表
        val currentTemplates = if (state.isSearching) state.filteredTemplates else state.templates

        // 边界检查
        if (intent.fromIndex !in currentTemplates.indices || intent.toIndex !in currentTemplates.indices) {
            return TemplateReducer.ReduceResult.stateOnly(state)
        }

        // 重新排序模板列表
        val reorderedTemplates = currentTemplates.toMutableList().apply {
            val movedItem = removeAt(intent.fromIndex)
            add(intent.toIndex, movedItem)
        }

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                draggedItemIndex = intent.fromIndex,
                dragTargetIndex = intent.toIndex,
                templates = if (state.isSearching) state.templates else reorderedTemplates,
                filteredTemplates = reorderedTemplates, // 🔥 修复：始终更新 filteredTemplates
            ),
        )
    }

    fun handleEndDragTemplate(intent: TemplateContract.Intent.EndDragTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 更新模板排序映射
        val currentTemplates = if (state.isSearching) state.filteredTemplates else state.templates
        val newOrder = currentTemplates.mapIndexed { index, template ->
            template.id to index
        }.toMap()

        val updatedTemplateOrder = state.templateOrder + newOrder

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isDragging = false,
                draggedItem = null,
                draggedItemIndex = null,
                dragTargetIndex = null,
                templateOrder = updatedTemplateOrder,
            ),
            // 🔥 修复：添加持久化排序 Effect
            effect = TemplateContract.Effect.SaveTemplateOrder(updatedTemplateOrder),
        )
    }

    fun handleCancelDragTemplate(intent: TemplateContract.Intent.CancelDragTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                isDragging = false,
                draggedItem = null,
                draggedItemIndex = null,
                dragTargetIndex = null,
            ),
        )
    }

    fun handleStartDragDraft(intent: TemplateContract.Intent.StartDragDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                isDragging = true,
                draggedItem = intent.draftId,
                draggedItemIndex = intent.currentIndex,
                dragTargetIndex = intent.currentIndex,
            ),
        )
    }

    fun handleUpdateDragDraft(intent: TemplateContract.Intent.UpdateDragDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 获取当前过滤后的草稿列表
        val currentDrafts = if (state.isSearching) state.filteredDrafts else state.drafts

        // 边界检查
        if (intent.fromIndex !in currentDrafts.indices || intent.toIndex !in currentDrafts.indices) {
            return TemplateReducer.ReduceResult.stateOnly(state)
        }

        // 重新排序草稿列表
        val reorderedDrafts = currentDrafts.toMutableList().apply {
            val movedItem = removeAt(intent.fromIndex)
            add(intent.toIndex, movedItem)
        }

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                draggedItemIndex = intent.fromIndex,
                dragTargetIndex = intent.toIndex,
                drafts = if (state.isSearching) state.drafts else reorderedDrafts,
                filteredDrafts = reorderedDrafts, // 🔥 修复：始终更新 filteredDrafts
            ),
        )
    }

    fun handleEndDragDraft(intent: TemplateContract.Intent.EndDragDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 更新草稿排序映射
        val currentDrafts = if (state.isSearching) state.filteredDrafts else state.drafts
        val newOrder = currentDrafts.mapIndexed { index, draft ->
            draft.id to index
        }.toMap()

        val updatedDraftOrder = state.draftOrder + newOrder

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isDragging = false,
                draggedItem = null,
                draggedItemIndex = null,
                dragTargetIndex = null,
                draftOrder = updatedDraftOrder,
            ),
            // 🔥 修复：添加持久化排序 Effect
            effect = TemplateContract.Effect.SaveDraftOrder(updatedDraftOrder),
        )
    }

    fun handleCancelDragDraft(intent: TemplateContract.Intent.CancelDragDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                isDragging = false,
                draggedItem = null,
                draggedItemIndex = null,
                dragTargetIndex = null,
            ),
        )
    }

    // === 一键置顶处理函数 ===

    fun handleMoveTemplateToTop(intent: TemplateContract.Intent.MoveTemplateToTop, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentTemplates = if (state.isSearching) state.filteredTemplates else state.templates
        val templateIndex = currentTemplates.indexOfFirst { it.id == intent.templateId }

        if (templateIndex <= 0) {
            // 已经在顶部或未找到
            return TemplateReducer.ReduceResult.stateOnly(state)
        }

        val template = currentTemplates[templateIndex]
        val reorderedTemplates = listOf(template) + currentTemplates.filterIndexed { index, _ -> index != templateIndex }

        // 更新排序映射
        val newOrder = reorderedTemplates.mapIndexed { index, t -> t.id to index }.toMap()

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                templates = if (state.isSearching) state.templates else reorderedTemplates,
                filteredTemplates = reorderedTemplates, // 🔥 修复：始终更新 filteredTemplates
                templateOrder = state.templateOrder + newOrder,
            ),
            effect = TemplateContract.Effect.AnimateMoveToTop(intent.templateId),
        )
    }

    fun handleMoveDraftToTop(intent: TemplateContract.Intent.MoveDraftToTop, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val currentDrafts = if (state.isSearching) state.filteredDrafts else state.drafts
        val draftIndex = currentDrafts.indexOfFirst { it.id == intent.draftId }

        if (draftIndex <= 0) {
            // 已经在顶部或未找到
            return TemplateReducer.ReduceResult.stateOnly(state)
        }

        val draft = currentDrafts[draftIndex]
        val reorderedDrafts = listOf(draft) + currentDrafts.filterIndexed { index, _ -> index != draftIndex }

        // 更新排序映射
        val newOrder = reorderedDrafts.mapIndexed { index, d -> d.id to index }.toMap()

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                drafts = if (state.isSearching) state.drafts else reorderedDrafts,
                filteredDrafts = reorderedDrafts, // 🔥 修复：始终更新 filteredDrafts
                draftOrder = state.draftOrder + newOrder,
            ),
            effect = TemplateContract.Effect.AnimateMoveToTop(intent.draftId),
        )
    }

    // === 私有辅助函数 ===

    /**
     * 应用用户自定义排序
     * 根据 sortOrder 映射对列表进行排序，没有排序信息的项目保持原有顺序并排在最后
     */
    private fun <T> applySortOrder(items: List<T>, sortOrder: Map<String, Int>): List<T>
        where T : Any {
        return items.sortedWith { a, b ->
            val aId = when (a) {
                is WorkoutTemplateDto -> a.id
                is com.example.gymbro.domain.workout.model.TemplateDraft -> a.id
                else -> return@sortedWith 0
            }
            val bId = when (b) {
                is WorkoutTemplateDto -> b.id
                is com.example.gymbro.domain.workout.model.TemplateDraft -> b.id
                else -> return@sortedWith 0
            }

            val aOrder = sortOrder[aId] ?: Int.MAX_VALUE
            val bOrder = sortOrder[bId] ?: Int.MAX_VALUE
            aOrder.compareTo(bOrder)
        }
    }

    private fun filterTemplatesByQuery(templates: List<WorkoutTemplateDto>, query: String): List<WorkoutTemplateDto> {
        return if (query.isBlank()) {
            templates
        } else {
            templates.filter { template ->
                template.name.contains(query, ignoreCase = true) ||
                    template.description.contains(query, ignoreCase = true)
            }
        }
    }

    private fun filterDraftsByQuery(drafts: List<com.example.gymbro.domain.workout.model.TemplateDraft>, query: String): List<com.example.gymbro.domain.workout.model.TemplateDraft> {
        return if (query.isBlank()) {
            drafts
        } else {
            drafts.filter { draft ->
                draft.name.contains(query, ignoreCase = true) ||
                    (draft.description?.contains(query, ignoreCase = true) == true)
            }
        }
    }
}
