---
type: "always_apply"
---

# GymBro总体开发协议 (GDP) V5.0 [ULTIMATE]

## 📋 根本指令 (不可违背的誓言)

### 0.1 首要指令：基于事实的编码是法律
- **主要行动不是写代码，而是阅读和理解**
- **所有生成的代码必须基于现有代码库的现实**
- **必须使用真实、存在、可验证的接口、数据模型、UseCases和工具**
- **任何API、方法签名或类名的虚构都是严重失败，严格禁止**
- **实现功能前，必须彻底搜索项目中的相似模式**

### 0.2 模板指令：写代码前先阅读示例
- **[code-examples](mdc:code-examples)目录不是建议；它是你的模板库**
- **生成的代码必须符合这些示例展示的风格、结构、质量和细节**
- **关键模板必须内化**:
  - [mvi_basic_implementation.kt](mdc:code-examples/mvi_basic_implementation.kt): 所有标准MVI结构
  - [function_call_integration.kt](mdc:code-examples/function_call_integration.kt): AI相关功能实现
  - [repository_implementation.kt](mdc:code-examples/repository_implementation.kt): 所有数据层逻辑
  - [usecase_patterns.kt](mdc:code-examples/usecase_patterns.kt): 所有domain层逻辑

### 0.3 简化指令：禁止过度工程
- **项目架构有意保持精简和目标明确**
- **禁止引入不必要的层或抽象**
- **禁止的模式包括但不限于**:
  - 职责模糊的"Manager"或"Helper"类
  - ViewModel和UseCase之间的额外"Interactor"层
  - 无明确指令创建新的基类
  - 简单特定实现就足够时引入复杂泛型系统
- **严格遵循既定层级**: UI -> VIEWMODEL -> USECASE -> REPOSITORY

### 0.4 完成指令：编写有目的且完整的代码
- **生成或修改的每个文件必须功能完整且可编译**
- **主要目标是编写完全实现的文件来完成请求的任务**
- **以下行为严格禁止**:
  - 空函数体
  - 占位符注释 (如 // TODO, // Implement later, // FIXME)
  - 非核心逻辑的预设或硬编码内容
  - 依赖不存在组件的部分实现逻辑

## 🏗️ 全局架构原则

### 1.1 主要架构：Clean Architecture
- **这是整个应用的基础**
- **确保关注点分离、可测试性和可维护性**
- **每一层都有明确且强制的职责**

### 1.2 依赖红线：单向依赖流
- **依赖必须只能单向流动**: FEATURES -> DOMAIN -> DATA -> CORE
- **这由构建系统强制执行，不可协商**
- **违规示例**:
  - ❌ 禁止：'domain'模块类从'features'模块导入
  - ❌ 禁止：'features'模块类直接从'data'模块导入
  - ✅ 允许：'features'模块类从'domain'模块导入

### 1.3 依赖注入 (DI)
- **Hilt是唯一且强制的DI框架**
- **所有依赖必须通过接口提供和注入，而非具体实现**
- **具体实现只在Hilt DI模块内绑定到接口**

### 1.4 错误处理框架
- **'Result<T>'包装器是处理可能失败操作的标准机制**
- **这种模式对所有数据和domain层的方法返回类型都是强制的**
- **它优雅地封装成功值或异常**
- **ViewModels和Reducers必须处理'Result'对象来相应更新UI状态**
- **ViewModels内禁止使用原始'try-catch'块进行业务流控制**

## 🔄 MVI架构核心协议

### 2.1 单向数据流 (UDF) 作为唯一UI模式
**数据流是神圣、不可变的循环**：
1. UI (用户交互) -> 创建并分发Intent
2. Intent -> ViewModel
3. ViewModel -> 将当前状态和Intent传递给Reducer
4. Reducer -> 纯计算并返回新状态和可选Effects
5. 新状态 -> ViewModel更新其StateFlow
6. StateFlow -> UI观察并重新渲染
7. Effects -> ViewModel分发给EffectHandler
8. EffectHandler -> 执行副作用 (如API调用)
9. 副作用结果 -> EffectHandler创建新的'...Result' Intent
10. 新Intent -> ViewModel (闭合循环)

### 2.2 基类要求
- **所有ViewModels必须继承项目的'BaseMviViewModel'。这不是可选的**

### 2.3 Contract对象
- **每个功能模块必须定义公共'Contract'对象**
- **此对象作为模块的公共API，清楚记录其能力**

### 2.4 State规则
- **State必须是@Immutable Kotlin数据类。这对Compose重组性能至关重要**
- **State类的所有属性必须声明为'val'。State必须深度不可变**
- **State类是整个屏幕的单一、全面的真实来源**
- **ViewModel必须只暴露一个StateFlow<State>。不允许多个状态流**

### 2.5 Intent规则
- **Intent表示"改变状态的意愿"**
- **命名必须是动词或动名词，描述行动** (如 LoadUserProfile, UpdateSettings)
- **来自EffectHandler的Intent必须以'...Result'后缀结尾**

### 2.6 Effect规则
- **Effect表示"执行副作用的命令"**
- **命名必须是动词，描述命令** (如 NavigateToHomeScreen, ShowErrorToast)
- **Effect是简单的数据承载对象。它是食谱，不是厨师**

## 🚫 严格禁止事项

### 代码质量禁令
- ❌ **代码捏造**: 任何形式的API或接口虚构
- ❌ **功能越界**: 创建任务范围外的额外功能
- ❌ **占位内容**: TODO、FIXME或任何未完成标记
- ❌ **尺寸违规**: 超过函数80行或文件500行限制
- ❌ **结构违规**: 在根目录创建任何文件
- ❌ **设计系统违规**: 使用硬编码值而非Tokens.*

### 架构禁令
- ❌ **跨层违规**: 违反单向依赖流
- ❌ **状态违规**: 多个StateFlow或可变状态
- ❌ **副作用违规**: 在Reducer中执行副作用
- ❌ **命名违规**: 不符合既定约定

## ✅ 强制要求

### 组件职责
- **ViewModel**: 协调者，持有状态，分发意图和效果，委托业务逻辑给domain层
- **Reducer**: 纯状态转换器，给定相同输入必须产生相同输出
- **EffectHandler**: 副作用执行器，与"外部世界"交互的唯一组件

### 设计系统和核心可重用性
- **禁止硬编码设计值**: 零容忍规则。所有设计值必须通过'designSystem'模块的'Tokens'系统访问
- **重用核心工具**: 在编写新工具函数前，必须先详尽检查'core'模块的现有解决方案

### 代码整洁和结构
- **禁止占位符注释**: 最终提交的代码必须无'TODO'、'FIXME'或'XXX'注释
- **单一职责原则**: 适用于类和函数
- **保持小巧**: 针对小而专注的函数，避免超过30-40行的函数

## 📊 测试与验证协议

### 测试理念
- **测试不是事后考虑；它们是实现的一部分**
- **Domain层**: 测试纯业务逻辑
- **Data层**: 测试repository逻辑、数据映射和与数据源的交互
- **ViewModel/Reducer**: 使用Turbine等库测试状态转换和效果发射

### 最低测试覆盖阈值 (强制)
- **Domain层**: >= 90%
- **Data层**: >= 80%
- **ViewModel & Reducer**: >= 75%

### 最终质量检查点 (完成定义)
任务或功能被认为'完成'当且仅当以下所有命令成功通过无警告无错误：
1. './gradlew formatCodeAll'
2. './gradlew qualityCheckAll'
3. './gradlew testAll'
4. './gradlew coverageCheck'
5. './gradlew assembleDebug'

## 📋 助手必须行动

1. **搜索存储库树并在添加代码前引用现有符号**
2. **扩展或修改最接近的现有文件；从不创建重复实现**
3. **只返回最小的git-diff (文件路径 + @@ 头部 + 更改行)**
4. **精确保留包名、Gradle模块和架构层边界**
5. **如果请求的行为已经存在，回应"NO CHANGE NEEDED"**

这是GymBro开发的完整协议。所有代码生成、修改和审查都必须严格遵循这些不可协商的原则。


### END OF PROTOCOL ###
