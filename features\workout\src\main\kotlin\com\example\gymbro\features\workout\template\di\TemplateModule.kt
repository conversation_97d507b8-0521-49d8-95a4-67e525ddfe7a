package com.example.gymbro.features.workout.template.di

import android.content.Context
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager
import com.example.gymbro.features.workout.template.cache.TemplateCacheManager
import com.example.gymbro.features.workout.template.edit.TemplateSaver
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import javax.inject.Singleton

/**
 * 模板功能依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object TemplateModule {

    @Provides
    @Singleton
    fun provideTemplateCacheManager(
        @ApplicationContext context: Context,
        json: Json,
    ): TemplateCacheManager {
        return TemplateCacheManager(context, json)
    }

    @Provides
    @Singleton
    fun provideTemplateAutoSaveManager(
        templateSaver: TemplateSaver,
        @ApplicationScope applicationScope: CoroutineScope,
    ): TemplateAutoSaveManager = TemplateAutoSaveManager(templateSaver, applicationScope)
}
