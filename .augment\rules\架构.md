---
type: "always_apply"
---

# GymBro 架构规范与工作流优化

## 🏗️ 模块架构总览 (21 模块)

### 核心模块 (Foundation)
- **:core** - 错误处理 (ModernResult<T>), 全局Timber日志, 工具类, promptbuilder
- **:core-arch** - MVI基础组件 (BaseMviViewModel, MviContract)
- **:core-network** - WebSocket, REST API, 网络监控
- **:core-ml** - AI/ML引擎 (BGE向量, TensorFlow Lite)

### 业务逻辑层
- **:domain** - 纯Kotlin UseCase接口, 实体 (使用Logger接口)
- **:data** - Repository实现, Room数据库, API客户端
- **:shared-models** - 纯DTO类与kotlinx.serialization

### 功能模块 (Presentation)
- **:features:profile** - ⭐ API架构标准 (ProfileNavigatable, ProfileFeatureApi)
- **:features:coach** - MVI 2.0实现, AI聊天流式处理
- **:features:auth** - 认证流程
- **:features:workout** - 训练管理
- **:features:home** - 仪表板和导航
- **:features:subscription** - 账单和高级功能
- **:features:thinkingbox** - AI流式处理
- **:features:exercise-library** - 运动库和搜索

### UI & 导航
- **:designSystem** - Compose组件, 主题, tokens
- **:navigation** - 路由定义和导航逻辑
- **:di** - Hilt依赖注入模块

### 基础设施
- **:worker** - 后台任务 (WorkManager)
- **:benchmark** - 性能测试
- **:utils** - 开发工具和脚本
- **:config** - 配置管理
- **:ci-scripts** - CI/CD自动化
- **:backup** - 数据备份和恢复

## 🚫 严格依赖约束

### 强制依赖流
```
:app → :features:* → :domain ← :data
     → :designSystem
     → :navigation
     → :di
```

### 绝对禁止的依赖
- ❌ Features **不能**直接依赖其他features
- ❌ Domain **不能**依赖Android框架
- ❌ Data **不能**依赖presentation层
- ❌ Core模块 **不能**依赖feature模块

### 必需依赖
- ✅ 所有features **必须**依赖 :domain 和 :core
- ✅ MVI features **必须**依赖 :core-arch
- ✅ AI features **必须**依赖 :core-ml
- ✅ Network features **必须**依赖 :core-network

## 🔄 工作流阶段优化

### 早期阶段 (文档导向)
基于 [EARLY_STAGE_WORKFLOW_OPTIMIZATION.md](mdc:.claude/EARLY_STAGE_WORKFLOW_OPTIMIZATION.md) 优化：

#### 第1阶段：难度评估 (Difficulty Assessment)
- **仅文档阅读**: 模块README和架构文档
- **禁止代码分析**: 不读取.kt源代码文件
- **允许文档范围**:
  - [core/README.md](mdc:core/README.md) (错误处理，Timber日志)
  - [core-network/README.md](mdc:core-network/README.md) (网络层)
  - [shared-models/README.md](mdc:shared-models/README.md) (纯DTO模型)
  - [designSystem/README.md](mdc:designSystem/README.md) (Tokens设计系统)
  - [code-examples/GymBro-MVI-Golden-Standard.md](mdc:code-examples/GymBro-MVI-Golden-Standard.md)
  - [code-examples/README.md](mdc:code-examples/README.md)

#### 第2阶段：Prompt重写 (Prompt Rewriter)
- **仅基于文档**: 架构理解和规范确认
- **职责限制**:
  1. 架构理解：基于MVI标准文档
  2. 模块职责识别：通过README了解各模块职责
  3. 设计规范确认：确认设计系统规范
  4. 示例模式参考：参考code-examples标准模式

### 执行阶段 (代码导向)

#### Task Planner & Context Processor 融合
基于 [PLANNER_CONTEXT_FUSION.md](mdc:.claude/PLANNER_CONTEXT_FUSION.md) 优化：

##### Task Planner 聚焦职责
- ✅ 读取模块README了解职责
- ✅ 识别核心*.kt文件（Contract、ViewModel、Repository、UseCase等）
- ✅ 输出精确文件列表给Context Processor
- ❌ **严禁全目录扫描**和深度文件遍历

##### 限制读取范围
```yaml
允许读取:
- {module}/README.md              # 模块说明
- {module}/*Contract*.kt          # 契约定义
- {module}/*ViewModel*.kt         # ViewModel层
- {module}/*Repository*.kt        # Repository接口/实现
- domain/*/{module}/*UseCase*.kt  # UseCase实现

禁止操作:
- 🚫 list_dir全目录扫描
- 🚫 递归读取所有*.kt文件
- 🚫 深入internal包结构
- 🚫 读取测试文件或配置文件
```

##### Context Processor 精确提取
- ✅ 接收Task Planner的precise_file_list
- ✅ 使用Claude-3.5-Haiku深度分析指定文件
- ✅ 提取函数签名、依赖关系、MVI组件
- ❌ **不进行额外文件搜索或发现**

## 🎯 MVI 2.0 架构标准

### Profile模块参考标准
```kotlin
// Contract定义
object FeatureContract {
    data class State(val isLoading: Boolean = false, val error: UiText? = null)
    sealed interface Intent
    sealed interface Effect
}

// Reducer (纯函数)
class FeatureReducer @Inject constructor() {
    fun reduce(intent: Intent, state: State): ReduceResult<State, Effect>
}

// EffectHandler (副作用)
class FeatureEffectHandler @Inject constructor() {
    suspend fun handle(intent: Intent, state: State): Flow<Effect>
}

// ViewModel (协调)
class FeatureViewModel @Inject constructor(
    private val reducer: FeatureReducer,
    private val effectHandler: FeatureEffectHandler
) : BaseMviViewModel<Intent, State, Effect>(initialState)
```

### API设计模式标准
```kotlin
// 导航契约
interface FeatureNavigatable {
    val route: String
    fun registerGraph(navGraphBuilder: NavGraphBuilder, navController: NavController)
}

// 功能服务API
interface FeatureApi {
    fun observeData(): Flow<DataModel>
    suspend fun performAction(): ModernResult<Unit, ModernDataError>
}
```

## 📐 包结构标准

### 标准包布局
```
com.example.gymbro.{module}/
├── api/                    # 公共接口 (仅features)
├── internal/               # 内部实现
│   ├── presentation/       # UI层 (MVI组件)
│   ├── data/              # 数据层实现
│   └── di/                # 模块特定DI
└── {domain_specific}/     # 领域特定包
```

### 导入约定
- 使用绝对导入: `com.example.gymbro.core.error.ModernResult`
- Features从domain导入: `com.example.gymbro.domain.{module}.repository.{Name}Repository`
- 内部可见性用于实现
- 仅通过接口公开API

## 🔄 Tier路由策略

### 更新的路由矩阵
- **T0**: PR → PT(跳过CTX，直接实现)
- **T1**: PR → TP(聚焦) → EXEC/DBG → PT → QA(可选)
- **T2**: PR → TP(聚焦) → CTX(精确提取) → EXEC → PT → QA
- **T3**: PR → TP(聚焦) → CTX(精确提取) → EXEC → PT → QA
- **T4**: PR → TP(聚焦) → CTX(精确提取) → EXEC → CODE_EXEC → PT → QA(≥95%)

### 协作触发条件
- **T2及以上**: 必须经过TP→CTX协作流程
- **T1**: TP可选择跳过CTX直接到执行
- **T0**: 简单任务跳过代码分析阶段

## 📊 质量标准

### 测试要求
- Domain层: 90%+覆盖率 (纯Kotlin，易测试)
- Data层: 85%+覆盖率 (repository实现)
- Feature层: 80%+覆盖率 (MVI组件)

### 性能基准
- MVI状态更新: ≤16ms
- Intent处理: ≤100ms
- Effect处理: ≤50ms
- 网络请求: ≤1000ms

### 代码质量
- 零编译警告
- Detekt静态分析通过
- 公共API的KDoc覆盖
- 一致的错误处理模式

所有实现必须严格遵循这些架构原则，确保模块间的清晰分离和高质量的代码标准。

- KDoc coverage for public APIs
- Consistent error handling patterns
