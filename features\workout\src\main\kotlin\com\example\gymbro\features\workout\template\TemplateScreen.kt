@file:OptIn(ExperimentalFoundationApi::class)

package com.example.gymbro.features.workout.template

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.template.internal.components.*
import kotlinx.coroutines.delay

/**
 * 训练模板主界面 - P4阶段增强版
 *
 * 🎯 P4阶段新增功能:
 * - 搜索功能集成 (切换显示/隐藏)
 * - SharedElements转场准备
 * - 自动保存状态指示
 * - 崩溃恢复检测
 * - 滑动删除和拖动排序支持
 *
 * 🏗️ 架构原则:
 * - Clean Architecture + MVI 2.0模式
 * - 使用标准Material Design组件
 * - designSystem主题令牌集成
 * - 完整的状态管理和错误处理
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateScreen(
    onNavigateToTemplateDetail: (String) -> Unit,
    onNavigateToNewTemplate: () -> Unit,
    onNavigateToEditTemplate: (String) -> Unit,
    onNavigateToCreateDraft: () -> Unit,
    onNavigateToDraftEditor: (String) -> Unit,
    onNavigateToStartWorkout: (String) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val viewModel: TemplateViewModel = hiltViewModel()
    val uiState by viewModel.state.collectAsStateWithLifecycle()
    val lifecycleOwner = LocalLifecycleOwner.current

    // P4新增：搜索相关状态
    val searchFocusRequester = remember { FocusRequester() }

    // 🎯 黄金标准：参考Profile模块，只在初始化时加载数据
    LaunchedEffect(viewModel) {
        // 初始化加载所有数据，避免重复加载
        viewModel.loadTemplates()
        viewModel.loadDrafts()
        // 🔥 修复：重置导航状态，确保按钮可用
        viewModel.resetNavigationState()
    }

    // 🎯 黄金标准：只在tab切换时刷新对应数据
    LaunchedEffect(uiState.currentTab) {
        when (uiState.currentTab) {
            TemplateContract.TemplateTab.TEMPLATES -> viewModel.refreshTemplates()
            TemplateContract.TemplateTab.DRAFTS -> viewModel.refreshDrafts()
        }
    }

    // 🔥 修复：添加超时重置机制，防止navigationPending永久锁定
    LaunchedEffect(uiState.navigationPending) {
        if (uiState.navigationPending) {
            // 3秒后自动重置导航状态，防止永久锁定
            delay(3000)
            if (uiState.navigationPending) {
                println("🔧 [DEBUG] 导航状态超时，自动重置")
                viewModel.resetNavigationState()
            }
        }
    }

    // 🔥 修复：添加Effect处理，处理导航事件
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is TemplateContract.Effect.NavigateToNewTemplate -> {
                    println("🔥 [DEBUG] 收到NavigateToNewTemplate Effect")
                    onNavigateToNewTemplate()
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                is TemplateContract.Effect.NavigateToCreateDraft -> {
                    println("🔥 [DEBUG] 收到NavigateToCreateDraft Effect")
                    onNavigateToCreateDraft()
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                is TemplateContract.Effect.NavigateToTemplateDetail -> {
                    println("🔥 [DEBUG] 收到NavigateToTemplateDetail Effect: ${effect.templateId}")
                    onNavigateToTemplateDetail(effect.templateId)
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                is TemplateContract.Effect.NavigateToEditTemplate -> {
                    println("🔥 [DEBUG] 收到NavigateToEditTemplate Effect: ${effect.templateId}")
                    onNavigateToEditTemplate(effect.templateId)
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                is TemplateContract.Effect.NavigateToDraftEditor -> {
                    println("🔥 [DEBUG] 收到NavigateToDraftEditor Effect: ${effect.draftId}")
                    onNavigateToDraftEditor(effect.draftId)
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                is TemplateContract.Effect.NavigateBack -> {
                    println("🔥 [DEBUG] 收到NavigateBack Effect")
                    onNavigateBack()
                    // 🔥 修复：导航完成后重置navigationPending状态
                    viewModel.resetNavigationState()
                }
                else -> {
                    // 其他Effect不需要处理
                }
            }
        }
    }

    // P4新增：崩溃恢复对话框
    if (uiState.showCacheRestoreDialog) {
        val recoveryItems =
            uiState.cachedChanges.values.mapNotNull { template ->
                com.example.gymbro.features.workout.template.cache.RecoveryResult
                    .Found(
                        template = template,
                        isCrashRecovery = true,
                    ).toRecoveryItem()
            }

        if (recoveryItems.isNotEmpty()) {
            CrashRecoveryDialog(
                recoveryItems = recoveryItems,
                onRecoverItem = { item ->
                    item.template?.let { template ->
                        viewModel.restoreSpecificCache(template.id)
                    }
                },
                onRecoverAll = {
                    viewModel.restoreFromCache()
                },
                onDismiss = {
                    viewModel.hideCacheRestoreDialog()
                },
            )
        }
    }

    // 搜索栏获得焦点处理
    LaunchedEffect(uiState.showSearchField) {
        if (uiState.showSearchField) {
            searchFocusRequester.requestFocus()
        }
    }

    // 🎯 Box+LazyColumn+Surface架构 - 遵循designSystem规范
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        // === BackgroundLayer (背景层) ===
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.workoutColors.cardBackground,
        ) {
            // === ContentBox (内容容器) ===
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(bottom = Tokens.Spacing.Medium),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.None),
            ) {
                // === LazyColumn Item 1: TopAppBar ===
                item {
                    TopAppBar(
                        title = { Text("训练模板") },
                        navigationIcon = {
                            IconButton(onClick = onNavigateBack) {
                                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                            }
                        },
                        actions = {
                            // P4新增：搜索切换按钮
                            IconButton(
                                onClick = { viewModel.toggleSearch() },
                            ) {
                                Icon(
                                    imageVector = if (uiState.showSearchField) Icons.Default.Close else Icons.Default.Search,
                                    contentDescription = if (uiState.showSearchField) "关闭搜索" else "搜索模板",
                                )
                            }

                            // 🔥 修复：添加按钮 - 遵循MVI架构，通过ViewModel dispatch Intent
                            IconButton(
                                onClick = {
                                    println("🔧 [DEBUG] 创建模板按钮被点击，当前Tab: ${uiState.currentTab}")
                                    when (uiState.currentTab) {
                                        TemplateContract.TemplateTab.TEMPLATES -> {
                                            println("🔧 [DEBUG] dispatch NavigateToNewTemplate Intent")
                                            viewModel.dispatch(TemplateContract.Intent.NavigateToNewTemplate)
                                        }
                                        TemplateContract.TemplateTab.DRAFTS -> {
                                            println("🔧 [DEBUG] dispatch NavigateToCreateDraft Intent")
                                            viewModel.dispatch(TemplateContract.Intent.NavigateToCreateDraft)
                                        }
                                    }
                                },
                                enabled = !uiState.navigationPending, // 🔥 修复：防止重复点击
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription =
                                    when (uiState.currentTab) {
                                        TemplateContract.TemplateTab.TEMPLATES -> "创建新模板"
                                        TemplateContract.TemplateTab.DRAFTS -> "创建新草稿"
                                    },
                                )
                            }
                        },
                    )
                }

                // === LazyColumn Item 2: SearchBar (条件显示) ===
                item {
                    AnimatedVisibility(
                        visible = uiState.showSearchField,
                        enter = fadeIn(
                            animationSpec = tween(
                                durationMillis = MotionDurations.CONTENT_TRANSITION,
                                easing = MotionEasings.STANDARD,
                            ),
                        ) + expandVertically(
                            animationSpec = tween(
                                durationMillis = MotionDurations.CONTENT_TRANSITION,
                                easing = MotionEasings.STANDARD,
                            ),
                        ),
                        exit = fadeOut(
                            animationSpec = tween(
                                durationMillis = MotionDurations.CONTENT_TRANSITION,
                                easing = MotionEasings.STANDARD,
                            ),
                        ) + shrinkVertically(
                            animationSpec = tween(
                                durationMillis = MotionDurations.CONTENT_TRANSITION,
                                easing = MotionEasings.STANDARD,
                            ),
                        ),
                    ) {
                        SearchBar(
                            query = uiState.searchQuery,
                            onQueryChange = { query ->
                                viewModel.searchTemplates(query)
                            },
                            onClearQuery = {
                                viewModel.clearFilters()
                            },
                            focusRequester = searchFocusRequester,
                            modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = Tokens.Spacing.Medium,
                                    vertical = Tokens.Spacing.Small,
                                ),
                        )
                    }
                }

                // === LazyColumn Item 3: TabRow ===
                item {
                    TabRow(
                        selectedTabIndex = uiState.currentTab.ordinal,
                        modifier = Modifier.fillMaxWidth(),
                        containerColor = MaterialTheme.workoutColors.cardBackground,
                        contentColor = MaterialTheme.workoutColors.accentPrimary,
                    ) {
                        TemplateContract.TemplateTab.values().forEach { tab ->
                            Tab(
                                selected = uiState.currentTab == tab,
                                onClick = { viewModel.switchTab(tab) },
                                text = { Text(tab.displayName) },
                            )
                        }
                    }
                }

                // === LazyColumn Item 4: Tab内容 (条件渲染) ===
                when (uiState.currentTab) {
                    TemplateContract.TemplateTab.TEMPLATES -> {
                        // 模板Tab内容
                        when {
                            uiState.error != null -> {
                                item {
                                    TemplateErrorView(
                                        error = uiState.error!!,
                                        onRetry = { viewModel.loadTemplates() },
                                        onDismiss = { viewModel.clearError() },
                                    )
                                }
                            }
                            uiState.filteredTemplates.isEmpty() && !uiState.isLoading -> {
                                item {
                                    if (uiState.isSearching) {
                                        EmptySearchResultsView(
                                            searchQuery = uiState.searchQuery,
                                            onClearSearch = { viewModel.clearError() },
                                        )
                                    } else {
                                        EmptyTemplatesView(
                                            onCreateTemplate = onNavigateToNewTemplate,
                                        )
                                    }
                                }
                            }
                            else -> {
                                // 🔥 修复：在 LazyColumn 层级实现拖动排序功能
                                itemsIndexed(
                                    items = uiState.filteredTemplates,
                                    key = { _, template -> template.id },
                                ) { index, template ->
                                    val hapticFeedback = LocalHapticFeedback.current

                                    TemplatePreviewCard(
                                        template = template,
                                        currentIndex = index,
                                        isDragging = uiState.isDragging && uiState.draggedItem == template.id,
                                        onItemClick = onNavigateToTemplateDetail,
                                        onEditClick = onNavigateToEditTemplate,
                                        onDeleteClick = { templateId ->
                                            viewModel.dispatch(
                                                TemplateContract.Intent.DeleteTemplate(templateId),
                                            )
                                        },
                                        onMoveToTopClick = { templateId ->
                                            viewModel.dispatch(
                                                TemplateContract.Intent.MoveTemplateToTop(templateId),
                                            )
                                        },
                                        modifier = Modifier
                                            .animateItem(
                                                fadeInSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                                fadeOutSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                                placementSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                            )
                                            // 🔥 修复：长按拖动排序功能
                                            .pointerInput(template.id) {
                                                var totalDragOffset = 0f
                                                var lastTargetIndex = index

                                                detectDragGestures(
                                                    onDragStart = { offset ->
                                                        totalDragOffset = 0f
                                                        lastTargetIndex = index
                                                        hapticFeedback.performHapticFeedback(
                                                            HapticFeedbackType.LongPress,
                                                        )
                                                        viewModel.dispatch(
                                                            TemplateContract.Intent.StartDragTemplate(
                                                                template.id,
                                                                index,
                                                            ),
                                                        )
                                                    },
                                                    onDrag = { change, dragAmount ->
                                                        // 🔥 修复：累积拖动偏移量
                                                        totalDragOffset += dragAmount.y

                                                        // 计算目标索引
                                                        val cardHeight = 80.dp.toPx()
                                                        val targetIndexOffset = (totalDragOffset / cardHeight).toInt()
                                                        val targetIndex = (index + targetIndexOffset)
                                                            .coerceAtLeast(0)
                                                            .coerceAtMost(uiState.filteredTemplates.size - 1)

                                                        // 只在目标索引变化时触发更新
                                                        if (targetIndex != lastTargetIndex) {
                                                            viewModel.dispatch(
                                                                TemplateContract.Intent.UpdateDragTemplate(
                                                                    lastTargetIndex,
                                                                    targetIndex,
                                                                ),
                                                            )
                                                            lastTargetIndex = targetIndex
                                                        }
                                                    },
                                                    onDragEnd = {
                                                        viewModel.dispatch(
                                                            TemplateContract.Intent.EndDragTemplate(
                                                                template.id,
                                                                lastTargetIndex,
                                                            ),
                                                        )
                                                        totalDragOffset = 0f
                                                    },
                                                )
                                            },
                                    )
                                }
                            }
                        }
                    }
                    TemplateContract.TemplateTab.DRAFTS -> {
                        // 草稿Tab内容
                        when {
                            uiState.error != null -> {
                                item {
                                    TemplateErrorView(
                                        error = uiState.error!!,
                                        onRetry = { viewModel.loadDrafts() },
                                        onDismiss = { viewModel.clearError() },
                                    )
                                }
                            }
                            uiState.filteredDrafts.isEmpty() && !uiState.isLoadingDrafts -> {
                                item {
                                    if (uiState.isSearching) {
                                        EmptySearchResultsView(
                                            searchQuery = uiState.searchQuery,
                                            onClearSearch = { viewModel.clearError() },
                                        )
                                    } else {
                                        EmptyDraftsView(
                                            onCreateDraft = onNavigateToCreateDraft,
                                        )
                                    }
                                }
                            }
                            else -> {
                                // 🔥 修复：在 LazyColumn 层级实现草稿拖动排序功能
                                itemsIndexed(
                                    items = uiState.filteredDrafts,
                                    key = { _, draft -> draft.id },
                                ) { index, draft ->
                                    val hapticFeedback = LocalHapticFeedback.current

                                    TemplatePreviewCard(
                                        draft = draft,
                                        currentIndex = index,
                                        isDragging = uiState.isDragging && uiState.draggedItem == draft.id,
                                        onItemClick = onNavigateToDraftEditor,
                                        onEditClick = onNavigateToDraftEditor,
                                        onDeleteClick = { draftId ->
                                            viewModel.dispatch(TemplateContract.Intent.DeleteDraft(draftId))
                                        },
                                        onMoveToTopClick = { draftId ->
                                            viewModel.dispatch(
                                                TemplateContract.Intent.MoveDraftToTop(draftId),
                                            )
                                        },
                                        modifier = Modifier
                                            .animateItem(
                                                fadeInSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                                fadeOutSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                                placementSpec = tween(
                                                    durationMillis = MotionDurations.CONTENT_TRANSITION,
                                                    easing = MotionEasings.STANDARD,
                                                ),
                                            )
                                            // 🔥 修复：长按拖动排序功能
                                            .pointerInput(draft.id) {
                                                var totalDragOffset = 0f
                                                var lastTargetIndex = index

                                                detectDragGestures(
                                                    onDragStart = { offset ->
                                                        totalDragOffset = 0f
                                                        lastTargetIndex = index
                                                        hapticFeedback.performHapticFeedback(
                                                            HapticFeedbackType.LongPress,
                                                        )
                                                        viewModel.dispatch(
                                                            TemplateContract.Intent.StartDragDraft(
                                                                draft.id,
                                                                index,
                                                            ),
                                                        )
                                                    },
                                                    onDrag = { change, dragAmount ->
                                                        // 🔥 修复：累积拖动偏移量
                                                        totalDragOffset += dragAmount.y

                                                        // 计算目标索引
                                                        val cardHeight = 80.dp.toPx()
                                                        val targetIndexOffset = (totalDragOffset / cardHeight).toInt()
                                                        val targetIndex = (index + targetIndexOffset)
                                                            .coerceAtLeast(0)
                                                            .coerceAtMost(uiState.filteredDrafts.size - 1)

                                                        // 只在目标索引变化时触发更新
                                                        if (targetIndex != lastTargetIndex) {
                                                            viewModel.dispatch(
                                                                TemplateContract.Intent.UpdateDragDraft(
                                                                    lastTargetIndex,
                                                                    targetIndex,
                                                                ),
                                                            )
                                                            lastTargetIndex = targetIndex
                                                        }
                                                    },
                                                    onDragEnd = {
                                                        viewModel.dispatch(
                                                            TemplateContract.Intent.EndDragDraft(
                                                                draft.id,
                                                                lastTargetIndex,
                                                            ),
                                                        )
                                                        totalDragOffset = 0f
                                                    },
                                                )
                                            },
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }

        // 🔥 修复：自动保存状态指示器 - 左下角小窗提示，不影响TopBar点击
        FloatingAutoSaveIndicator(
            state = uiState.autoSaveState,
            errorMessage = uiState.saveError?.toString(),
            onRetry = { /* viewModel.retryLastSave() */ },
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(Tokens.Spacing.Medium),
        )
    }
}

// 🔥 已删除：TemplateListItem、SimpleTemplateItem、SimpleDraftItem 组件
// 统一使用 TemplatePreviewCard 组件，消除重复实现，提供完整统计信息展示

/**
 * P4新增：搜索栏组件
 */
@Composable
private fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onClearQuery: () -> Unit,
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier,
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = { Text("搜索模板或草稿...") },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "搜索",
            )
        },
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(onClick = onClearQuery) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除搜索",
                    )
                }
            }
        },
        keyboardOptions =
        KeyboardOptions(
            imeAction = ImeAction.Search,
        ),
        keyboardActions =
        KeyboardActions(
            onSearch = {
                keyboardController?.hide()
            },
        ),
        singleLine = true,
        modifier = modifier.focusRequester(focusRequester),
    )
}

/**
 * 模板Tab内容 - 已弃用，使用LazyColumn items替代
 *
 * @deprecated 使用LazyColumn items替代，保留此函数仅用于参考
 */
@Deprecated("使用LazyColumn items替代，保留此函数仅用于参考")
@Composable
private fun TemplateTabContent(
    uiState: TemplateContract.State,
    onTemplateClick: (String) -> Unit,
    onEditTemplate: (String) -> Unit,
    onDeleteTemplate: (String) -> Unit,
    onCreateTemplate: () -> Unit,
    onRetry: () -> Unit,
    onClearError: () -> Unit,
) {
    // 已迁移到LazyColumn items中
}

/**
 * 草稿Tab内容 - 已弃用，使用LazyColumn items替代
 *
 * @deprecated 使用LazyColumn items替代，保留此函数仅用于参考
 */
@Deprecated("使用LazyColumn items替代，保留此函数仅用于参考")
@Composable
private fun DraftTabContent(
    uiState: TemplateContract.State,
    onDraftClick: (String) -> Unit,
    onEditDraft: (String) -> Unit,
    onDeleteDraft: (String) -> Unit,
    onPromoteDraft: (String) -> Unit,
    onCreateDraft: () -> Unit,
    onRetry: () -> Unit,
    onClearError: () -> Unit,
) {
    // 已迁移到LazyColumn items中
}

// === Preview组件 ===

/**
 * TemplateScreen预览 - 使用Box+LazyColumn+Surface架构
 */
@GymBroPreview
@Composable
private fun TemplateScreenPreview() {
    GymBroTheme {
        TemplateScreen(
            onNavigateToTemplateDetail = { },
            onNavigateToNewTemplate = { },
            onNavigateToEditTemplate = { },
            onNavigateToCreateDraft = { },
            onNavigateToDraftEditor = { },
            onNavigateToStartWorkout = { },
            onNavigateBack = { },
        )
    }
}
