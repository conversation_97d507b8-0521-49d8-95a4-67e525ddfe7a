package com.example.gymbro.features.workout.calendar.internal.components

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.repository.CalendarRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Calendar自动保存用例
 *
 * 🎯 功能特性：
 * - 为Calendar ViewModel提供自动保存接口
 * - 集成CalendarRepository实现真实的保存功能
 * - 支持会话生命周期管理
 * - 处理拖拽操作的自动保存
 *
 * @param logger 日志记录器
 * @param calendarRepository 日历数据仓库
 */
@Singleton
class CalendarAutoSaveUseCase
@Inject
constructor(
    private val logger: Logger,
    private val calendarRepository: CalendarRepository,
) {
    /**
     * 创建日历自动保存会话
     *
     * @param userId 用户ID
     * @param scope ViewModel协程作用域
     * @return 会话ID
     */
    suspend operator fun invoke(
        userId: String,
        scope: CoroutineScope,
    ): ModernResult<String> = withContext(Dispatchers.IO) {
        logger.d("CalendarAutoSaveUseCase", "创建日历自动保存会话: $userId")

        try {
            // 生成会话ID
            val sessionId = "calendar_session_${userId}_${System.currentTimeMillis()}"

            // 这里可以实现会话管理逻辑，比如清理旧会话等
            logger.d("CalendarAutoSaveUseCase", "自动保存会话创建成功: $sessionId")

            ModernResult.Success(sessionId)
        } catch (e: Exception) {
            logger.e(e, "创建自动保存会话失败")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "createAutoSaveSession",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString(
                        "创建自动保存会话失败: ${e.message}",
                    ),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 保存日历项目（拖拽时使用）
     *
     * @param sessionId 会话ID
     * @param item 日历项目
     * @return 保存结果
     */
    suspend fun saveItem(
        sessionId: String,
        item: CalendarItem,
    ): ModernResult<Unit> = withContext(Dispatchers.IO) {
        logger.d("CalendarAutoSaveUseCase", "保存日历项目: ${item.id}")

        try {
            // 使用CalendarRepository保存项目
            val result = calendarRepository.updateCalendarItem(item)

            when (result) {
                is ModernResult.Success -> {
                    logger.d("CalendarAutoSaveUseCase", "日历项目保存成功: ${item.id}")
                    ModernResult.Success(Unit)
                }
                is ModernResult.Error -> {
                    logger.e("CalendarAutoSaveUseCase", "日历项目保存失败: ${result.error.message}")
                    result
                }
                is ModernResult.Loading -> {
                    // 不应该出现Loading状态，但为了完整性处理
                    ModernResult.Error(
                        com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                            operationName = "saveCalendarItem",
                            message = com.example.gymbro.core.ui.text.UiText.DynamicString("保存操作超时"),
                            recoverable = true,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            logger.e(e, "保存日历项目失败")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "saveCalendarItem",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString(
                        "保存日历项目失败: ${e.message}",
                    ),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 恢复日历项目（应用启动时使用）
     *
     * @param sessionId 会话ID
     * @return 恢复的日历项目
     */
    suspend fun restoreItem(
        sessionId: String,
    ): ModernResult<CalendarItem?> = withContext(Dispatchers.IO) {
        logger.d("CalendarAutoSaveUseCase", "恢复日历项目: $sessionId")

        try {
            // 这里可以实现从临时存储恢复项目的逻辑
            // 目前简化实现，返回null表示没有需要恢复的项目
            logger.d("CalendarAutoSaveUseCase", "没有需要恢复的日历项目")
            ModernResult.Success(null)
        } catch (e: Exception) {
            logger.e(e, "恢复日历项目失败")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "restoreCalendarItem",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString(
                        "恢复日历项目失败: ${e.message}",
                    ),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 删除自动保存会话（ViewModel销毁时使用）
     *
     * @param sessionId 会话ID
     * @return 删除结果
     */
    suspend fun deleteSession(sessionId: String): ModernResult<Unit> {
        logger.d("CalendarAutoSaveUseCase", "删除自动保存会话: $sessionId")
        return try {
            // 实现会话删除逻辑
            // 这里可以集成实际的数据删除操作
            logger.d("CalendarAutoSaveUseCase", "会话删除成功: $sessionId")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            logger.e("CalendarAutoSaveUseCase", "会话删除失败: $sessionId", e)
            ModernResult.Error(
                e.toModernDataError(
                    operationName = "deleteSession",
                    uiMessage = UiText.DynamicString("删除会话失败"),
                ),
            )
        }
    }
}
