package com.example.gymbro.features.workout.template.edit

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.error.CrossModuleErrorHandler
import com.example.gymbro.core.error.ErrorContextManager
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import com.example.gymbro.features.workout.logging.WorkoutLogTree
import com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.handlers.TemplateEditEffectHandler
import com.example.gymbro.features.workout.template.edit.handlers.TemplateEditSaveHandler
import com.example.gymbro.features.workout.template.edit.handlers.TemplateEditStateManager
import com.example.gymbro.features.workout.template.edit.handlers.TemplateEditTextInputHandler
import com.example.gymbro.features.workout.template.edit.transaction.TemplateTransactionManager
import com.example.gymbro.navigation.CrossModuleNavigator
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * TemplateEdit ViewModel - MVI 2.0 架构重构版
 *
 * 🎯 核心职责（重构后）：
 * - MVI 2.0 架构协调器
 * - Intent分发到专门的Handler
 * - Effect处理和UI反馈
 * - 轻量级状态协调
 *
 * 📋 架构标准：
 * - 继承BaseMviViewModel
 * - 使用TemplateEditReducer
 * - 委托模式分离关注点
 * - 单一职责原则
 *
 * 🔄 数据流：
 * UI → Intent → Handler → Reducer → State + Effect → UI
 *
 * 🔥 重构改进：
 * - 移除过时的JSON处理逻辑（已由JSON系统处理）
 * - 提取保存逻辑到SaveHandler
 * - 提取状态管理到StateManager
 * - 提取文本输入处理到TextInputHandler
 * - 保持ViewModel轻量化（<300行）
 */
@HiltViewModel
class TemplateEditViewModel @Inject constructor(
    override val reducer: TemplateEditReducer,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val autoSaveManager: TemplateAutoSaveManager,
    private val crossModuleNavigator: CrossModuleNavigator,
    private val crossModuleErrorHandler: CrossModuleErrorHandler,
    private val errorContextManager: ErrorContextManager,
    private val resourceProvider: ResourceProvider,
    private val savedStateHandle: SavedStateHandle,
    private val templateVersionUseCase: TemplateVersionUseCase,
    private val templateTransactionManager: TemplateTransactionManager,
    // 🔥 新增：专门的Handler依赖
    private val saveHandler: TemplateEditSaveHandler,
    private val stateManager: TemplateEditStateManager,
    private val textInputHandler: TemplateEditTextInputHandler,
    private val effectHandler: TemplateEditEffectHandler,
) : BaseMviViewModel<TemplateEditContract.Intent, TemplateEditContract.State, TemplateEditContract.Effect>(
    initialState = TemplateEditContract.State(),
) {

    companion object {
        private const val TEMPLATE_ID_KEY = "id"
    }

    // 🔥 重构：简化的状态管理
    private val templateId: String? = savedStateHandle.get<String>(TEMPLATE_ID_KEY)

    init {
        Timber.d("🚀 TemplateEditViewModel (MVI 2.0 重构版) initialized")
        initializeEditor()
        initializeEffectHandler()
        observeEffects()
        // 🔥 暂时禁用template自动保存功能
        Timber.d("🚫 [AUTO-SAVE-DISABLED] Template自动保存管理器初始化已跳过")
    }

    /**
     * 🔥 P3修复：增强的编辑器初始化，包含用户ID管理
     * 委托给StateManager处理复杂的初始化逻辑
     */
    private fun initializeEditor() {
        viewModelScope.launch {
            try {
                // P3修复：首先初始化状态并获取用户ID
                val initialState = stateManager.initializeState()
                Timber.d("✅ 初始状态设置完成，用户ID: ${initialState.currentUserId}")

                // 设置初始状态（包含用户ID）
                updateState { initialState }

                // 然后初始化模板
                stateManager.initializeTemplate(
                    templateId = templateId,
                    onTemplateLoaded = { template ->
                        dispatch(TemplateEditContract.Intent.SetTemplate(template))
                    },
                    onEmptyTemplateCreated = { templateId ->
                        dispatch(TemplateEditContract.Intent.CreateEmptyTemplate(templateId))
                    },
                )
            } catch (e: Exception) {
                Timber.e(e, "❌ 编辑器初始化失败")
                // 设置错误状态
                updateState { currentState ->
                    currentState.copy(
                        isLoading = false,
                        error = UiText.DynamicString("初始化失败: ${e.message}"),
                    )
                }
            }
        }
    }

    /**
     * 🔥 重构：统一的Intent分发逻辑
     * 根据Intent类型委托给相应的Handler处理
     */
    override fun dispatch(intent: TemplateEditContract.Intent) {
        Timber.tag("INTENT-DISPATCH").d("🎯 分发Intent: ${intent::class.simpleName}")

        when (intent) {
            // === 保存相关Intent：委托给SaveHandler ===
            is TemplateEditContract.Intent.SaveTemplate -> {
                viewModelScope.launch {
                    // 🔥 修改：智能保存逻辑 - 区分新建和更新场景
                    val isNewTemplate = currentState.template?.id.isNullOrBlank() ||
                        currentState.template?.id?.startsWith("temp_") == true

                    val (isDraft, isPublishing) = if (isNewTemplate) {
                        // 新创建的模板：首次保存为草稿
                        Timber.d("🆕 新模板首次保存，设置为草稿状态")
                        true to false
                    } else {
                        // 已存在的模板：保持原有状态
                        val originalIsDraft = currentState.template?.isDraft ?: true
                        val originalIsPublished = currentState.template?.isPublished ?: false
                        Timber.d(
                            "📝 更新现有模板，保持原状态: isDraft=$originalIsDraft, isPublished=$originalIsPublished",
                        )
                        originalIsDraft to originalIsPublished
                    }

                    saveHandler.handleSave(
                        currentState = currentState,
                        isDraft = isDraft,
                        isPublishing = isPublishing,
                        onSuccess = { templateId, template ->
                            handleSaveSuccess(templateId, template, isDraft, isPublishing)
                        },
                        onError = { error ->
                            sendEffect(TemplateEditContract.Effect.ShowError(error))
                        },
                    )
                }
            }
            // === 保存相关：通过标准MVI流程处理 (修复架构问题) ===
            is TemplateEditContract.Intent.SaveAsDraft,
            is TemplateEditContract.Intent.PublishTemplate,
            is TemplateEditContract.Intent.CreateAndSaveImmediately,
            -> {
                // 🔥 修复：通过Reducer处理，而不是直接在ViewModel中处理
                // 这将产生相应的Effect，然后由EffectHandler处理实际保存逻辑
                Timber.d("🔄 保存Intent通过MVI流程: ${intent::class.simpleName}")
                super.dispatch(intent)
            }

            // === 文本输入：委托给TextInputHandler ===
            is TemplateEditContract.Intent.UpdateTemplateName -> {
                textInputHandler.handleNameUpdate(
                    name = intent.name,
                    onUpdate = { debouncedName ->
                        super.dispatch(TemplateEditContract.Intent.UpdateTemplateName(debouncedName))
                    },
                )
            }
            is TemplateEditContract.Intent.UpdateTemplateDescription -> {
                textInputHandler.handleDescriptionUpdate(
                    description = intent.description,
                    onUpdate = { debouncedDescription ->
                        super.dispatch(
                            TemplateEditContract.Intent.UpdateTemplateDescription(debouncedDescription),
                        )
                    },
                )
            }

            // === 导航相关：委托给EffectHandler ===
            is TemplateEditContract.Intent.NavigateBack -> {
                effectHandler.handleNavigateBack(
                    currentState = currentState,
                    onPrepareExit = {
                        prepareToExit()
                    },
                    onNavigate = {
                        sendEffect(TemplateEditContract.Effect.NavigateBack)
                    },
                )
            }

            // === 其他Intent：传递给父类处理 ===
            else -> {
                super.dispatch(intent)
            }
        }
    }

    /**
     * 🔥 重构：简化的保存成功处理
     */
    private fun handleSaveSuccess(
        savedTemplateId: String,
        savedTemplate: com.example.gymbro.domain.workout.model.template.WorkoutTemplate,
        isDraft: Boolean,
        isPublishing: Boolean,
    ) {
        viewModelScope.launch {
            try {
                // 更新状态
                val updatedTemplate = savedTemplate.copy(
                    id = savedTemplateId,
                    isDraft = isDraft,
                    isPublished = isPublishing,
                    updatedAt = System.currentTimeMillis(),
                )

                dispatch(TemplateEditContract.Intent.SetTemplate(updatedTemplate))

                // 🔥 Phase 3 Fix: 根据操作类型分发不同的Intent，确保正确的状态同步
                if (isPublishing) {
                    dispatch(TemplateEditContract.Intent.PublishCompleted)
                } else {
                    dispatch(TemplateEditContract.Intent.SaveSuccess)
                }

                // 🔥 清理：移除保存成功Toast，使用左下角小窗提示
                val message = when {
                    isPublishing -> "模板已发布"
                    isDraft -> {
                        // 区分新建草稿和草稿更新
                        if (savedTemplate.createdAt == savedTemplate.updatedAt) {
                            "模板已创建并保存为草稿"
                        } else {
                            "草稿已更新"
                        }
                    }
                    else -> "模板已保存"
                }
                // 🚨 清理：不再发送Toast，依赖左下角小窗提示系统
                Timber.tag("SAVE-SUCCESS").d("🍞 保存成功消息被忽略，使用左下角提示: $message")

                // 非草稿保存后导航返回
                if (!isDraft) {
                    sendEffect(TemplateEditContract.Effect.NavigateBack)
                }
            } catch (e: Exception) {
                Timber.e(e, "❌ 保存成功后状态更新失败")
                sendEffect(
                    TemplateEditContract.Effect.ShowError(
                        com.example.gymbro.core.ui.text.UiText.DynamicString("保存成功，但状态更新失败"),
                    ),
                )
            }
        }
    }

    /**
     * 🔥 重构：简化的退出准备逻辑
     * 委托给StateManager处理复杂的退出逻辑
     */
    private fun prepareToExit() {
        stateManager.prepareToExit(
            currentState = currentState,
            onSaveRequired = { template ->
                viewModelScope.launch {
                    saveHandler.handleAutoSave(
                        template = template,
                        onSuccess = {
                            sendEffect(TemplateEditContract.Effect.NavigateBack)
                        },
                        onError = { error ->
                            sendEffect(TemplateEditContract.Effect.ShowError(error))
                            // 即使保存失败也允许退出
                            sendEffect(TemplateEditContract.Effect.NavigateBack)
                        },
                    )
                }
            },
            onDirectExit = {
                sendEffect(TemplateEditContract.Effect.NavigateBack)
            },
        )
    }

    /**
     * 🔥 重构：Effect处理初始化
     */
    override fun initializeEffectHandler() {
        viewModelScope.launch {
            effect.collect { effect ->
                // 🔥 保留关键 Effect 收集日志
                WorkoutLogTree.Template.debug("🔥 收到Effect: ${effect::class.simpleName}")

                effectHandler.handleEffect(
                    effect = effect,
                    currentState = currentState,
                    crossModuleNavigator = crossModuleNavigator,
                    resourceProvider = resourceProvider,
                    onSaveSuccess = { templateId, template ->
                        WorkoutLogTree.Template.info("🔥 保存成功回调: templateId=$templateId")
                        handleSaveSuccess(
                            templateId,
                            template,
                            isDraft = template.isDraft,
                            isPublishing = template.isPublished,
                        )
                    },
                    onSaveError = { error ->
                        WorkoutLogTree.Template.error("🔥 保存失败回调: $error")
                        sendEffect(TemplateEditContract.Effect.ShowError(error))
                    },
                )
            }
        }
    }

    /**
     * 🔥 重构：简化的Effect观察
     */
    private fun observeEffects() {
        // Effect处理已在initializeEffectHandler中统一处理
        // 这里可以添加额外的Effect观察逻辑（如果需要）
    }

    override fun onCleared() {
        super.onCleared()
        // 清理Handler资源
        textInputHandler.cleanup()
        stateManager.cleanup()
        saveHandler.cleanup()
        effectHandler.cleanup()
        Timber.d("🧹 TemplateEditViewModel 资源清理完成")
    }
}
