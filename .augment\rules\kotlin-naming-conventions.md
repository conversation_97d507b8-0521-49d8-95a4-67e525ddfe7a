---
type: "always_apply"
description: "Example description"
---
# Kotlin 命名规范

## 📋 核心命名原则

### 严格遵循 GymBro 项目标准
所有命名必须与 [code-examples](mdc:code-examples) 中的示例保持一致，确保项目范围内的命名规范统一性。

## 📦 包命名 (Packages)

### 规则
- **全部小写**，使用 `.` 作为分隔符
- **严禁使用下划线** `_`
- **遵循模块结构**: `com.example.gymbro.{module}.{domain_specific}`

```kotlin
// ✅ 正确示例
package com.example.gymbro.features.workout.internal.presentation
package com.example.gymbro.domain.user.repository
package com.example.gymbro.core.network.client

// ❌ 错误示例
package com.example.gymbro.Features.workout.data_source
package com.example.gymbro.domain.User_Repository
```

## 📄 文件命名 (Files)

### 规则
- **PascalCase**，与文件内核心声明的名称一致
- **必须反映主要类或接口名称**

```kotlin
// ✅ 正确示例
UserProfileContract.kt       // 如果文件内核心是 object UserProfileContract
WorkoutViewModel.kt          // 如果文件内核心是 class WorkoutViewModel
UserRepository.kt           // 如果文件内核心是 interface UserRepository

// ❌ 错误示例
userProfile.kt
workout_view_model.kt
```

## 🏗️ 类型声明 (Classes, Interfaces, Enums)

### 规则
- **使用 PascalCase**
- **遵循架构组件命名约定**

```kotlin
// ✅ 正确示例
class UserProfile
interface NetworkClient
enum class Status
annotation class Inject
object AppConstants
data class WorkoutState

// ❌ 错误示例
class userProfile
interface network_client
enum class status
```

## 🔧 函数命名 (Functions)

### 规则
- **使用 camelCase**，以小写字母开头
- **动词优先**，描述函数执行的操作

```kotlin
// ✅ 正确示例
fun calculateScore()
fun fetchUserData()
fun processIntent()
fun navigateToHome()

// ❌ 错误示例
fun CalculateScore()
fun fetch_user_data()
fun Process_Intent()
```

### MVI 相关函数命名
遵循 [MVI 架构标准](mdc:.cursor/rules/架构rule.mdc)：

```kotlin
// ✅ Intent 处理函数
fun processIntent(intent: WorkoutIntent)
fun handleUserAction(action: UserAction)

// ✅ Effect 处理函数
fun handleEffect(effect: WorkoutEffect)
fun executeEffect(effect: NavigationEffect)

// ✅ Reducer 函数
fun reduce(intent: Intent, state: State): ReduceResult<State, Effect>
```

## 🧪 测试函数命名 (Test Functions)

### 规则
- **使用反引号** `` ` `` 包裹自然语言描述
- **遵循 given-when-then 模式**

```kotlin
// ✅ 正确示例
@Test
fun `given invalid credentials, when login attempted, then should fail`() { ... }

@Test
fun `given valid workout data, when saving workout, then should return success`() { ... }

// ❌ 错误示例
@Test
fun testLoginWithInvalidCredentials() { ... }
```

## 📊 属性与变量命名 (Properties & Variables)

### 规则
- **使用 camelCase**，以小写字母开头
- **Backing Property**: 使用下划线 `_` 前缀
- **Boolean**: 使用 `is`, `has`, `can`, `should` 等作为前缀

```kotlin
// ✅ 正确示例
val userName: String
val isLoading: Boolean
val hasPermission: Boolean
val canEdit: Boolean
private val _items = mutableListOf<String>()
val items: List<String> = _items

// ❌ 错误示例
val UserName: String
val loading: Boolean        // 应该是 isLoading
val permission: Boolean     // 应该是 hasPermission
```

### StateFlow 和 State 属性
```kotlin
// ✅ MVI State 属性命名
data class WorkoutState(
    val isLoading: Boolean = false,
    val workoutList: List<Workout> = emptyList(),
    val selectedWorkout: Workout? = null,
    val errorMessage: String? = null,
    val hasChanges: Boolean = false
)

// ✅ ViewModel StateFlow 命名
private val _state = MutableStateFlow(WorkoutState())
val state: StateFlow<WorkoutState> = _state.asStateFlow()
```

## 📋 常量命名 (Constants)

### 规则
- **`const val` 或 `object` 内的 `val` 使用 `SCREAMING_SNAKE_CASE`**
- **性能相关常量**: 参考 `GlobalPerformanceConfig`

```kotlin
// ✅ 正确示例
const val MAX_RETRIES = 3
const val API_TIMEOUT_MS = 30000L
const val DEFAULT_PAGE_SIZE = 20

object WorkoutConstants {
    const val MIN_DURATION_MINUTES = 5
    const val MAX_SETS_PER_EXERCISE = 10
}

// ❌ 错误示例
const val maxRetries = 3
const val apiTimeout = 30000L
```

## 🏛️ 架构组件命名约定

### UseCase 命名
- **格式**: `动词 + 名词/名词短语 + UseCase`

```kotlin
// ✅ 正确示例
class GetUserProfileUseCase
class SaveWorkoutDataUseCase
class CalculateCaloriesBurnedUseCase
class ValidateLoginCredentialsUseCase

// ❌ 错误示例
class UserProfileUseCase      // 缺少动词
class GetUserProfile         // 缺少UseCase后缀
class getUserProfileUseCase  // 应该是PascalCase
```

### Repository 命名
- **接口**: `名词 + Repository`
- **实现类**: `名词 + RepositoryImpl`

```kotlin
// ✅ 正确示例
interface UserRepository
class UserRepositoryImpl : UserRepository

interface WorkoutRepository
class WorkoutRepositoryImpl : WorkoutRepository

// ❌ 错误示例
interface UserRepo           // 应该是完整的Repository
class UserRepositoryImplementation  // 应该是Impl
```

### ViewModel 命名
- **格式**: `功能模块名 + ViewModel`

```kotlin
// ✅ 正确示例
class UserProfileViewModel : BaseMviViewModel<Intent, State, Effect>
class WorkoutSessionViewModel : BaseMviViewModel<Intent, State, Effect>
class ExerciseLibraryViewModel : BaseMviViewModel<Intent, State, Effect>

// ❌ 错误示例
class UserVM                 // 应该是完整的ViewModel
class ProfileViewModel       // 应该包含完整的功能描述
```

### Contract 对象命名
```kotlin
// ✅ 正确示例
object UserProfileContract {
    data class State(
        val isLoading: Boolean = false,
        val userProfile: UserProfile? = null,
        val errorMessage: String? = null
    )

    sealed interface Intent {
        object LoadProfile : Intent
        data class UpdateProfile(val profile: UserProfile) : Intent
    }

    sealed interface Effect {
        object NavigateToSettings : Effect
        data class ShowError(val message: String) : Effect
    }
}
```

## 🎨 Jetpack Compose 命名约定

### Composable 函数
- **使用 PascalCase**
- **描述性命名**，反映UI组件功能

```kotlin
// ✅ 正确示例
@Composable
fun UserProfileCard(
    userProfile: UserProfile,
    onEditClick: () -> Unit
) { ... }

@Composable
fun WorkoutSessionTimer(
    duration: Duration,
    isRunning: Boolean,
    onStartStop: () -> Unit
) { ... }

// ❌ 错误示例
@Composable
fun userCard() { ... }       // 应该是PascalCase和更描述性

@Composable
fun Timer() { ... }          // 太泛泛，应该更具体
```

### Preview 函数命名
- **必须为 `private`**
- **命名**: `被预览组件名 + Preview`
- **必须使用项目统一预览注解**: `@GymBroPreview`

```kotlin
// ✅ 正确示例
@GymBroPreview
@Composable
private fun UserProfileCardPreview() {
    GymBroTheme {
        UserProfileCard(
            userProfile = UserProfile.sample(),
            onEditClick = {}
        )
    }
}

@GymBroPreview
@Composable
private fun WorkoutSessionTimerPreview() {
    GymBroTheme {
        WorkoutSessionTimer(
            duration = 30.minutes,
            isRunning = true,
            onStartStop = {}
        )
    }
}

// ❌ 错误示例
@Preview                     // 应该使用@GymBroPreview
@Composable
fun Preview() { ... }        // 应该是private和具体命名

@GymBroPreview
@Composable
fun UserCardPreview() { ... } // 应该是private
```

## 📐 Intent、Effect 和 State 命名规范

### Intent 命名
- **使用动词或动名词**
- **Result intent 必须以 'Result' 结尾**

```kotlin
// ✅ 正确示例
sealed interface WorkoutIntent {
    object LoadWorkouts : WorkoutIntent
    data class StartWorkout(val workoutId: String) : WorkoutIntent
    data class SaveProgress(val progress: WorkoutProgress) : WorkoutIntent

    // Result intents from EffectHandler
    data class LoadWorkoutsResult(val result: Result<List<Workout>>) : WorkoutIntent
    data class SaveProgressResult(val result: Result<Unit>) : WorkoutIntent
}

// ❌ 错误示例
sealed interface WorkoutIntent {
    object Workouts : WorkoutIntent          // 应该是LoadWorkouts
    data class Workout(val id: String) : WorkoutIntent  // 应该是StartWorkout
    data class LoadWorkoutsResponse(val result: Result<List<Workout>>) : WorkoutIntent  // 应该以Result结尾
}
```

### Effect 命名
- **使用动词**，描述要执行的命令

```kotlin
// ✅ 正确示例
sealed interface WorkoutEffect {
    object NavigateToWorkoutList : WorkoutEffect
    data class ShowErrorMessage(val message: String) : WorkoutEffect
    data class StartTimer(val duration: Duration) : WorkoutEffect
    object SaveWorkoutData : WorkoutEffect
}

// ❌ 错误示例
sealed interface WorkoutEffect {
    object WorkoutList : WorkoutEffect       // 应该是NavigateToWorkoutList
    data class Error(val message: String) : WorkoutEffect  // 应该是ShowErrorMessage
}
```

## 🔍 验证检查清单

### 每次命名时必须验证
1. ✅ 是否遵循对应组件的命名规范？
2. ✅ 是否与 [code-examples](mdc:code-examples) 中的模式一致？
3. ✅ 是否清晰描述了组件的职责和功能？
4. ✅ 是否符合项目的架构层级要求？
5. ✅ 是否避免了缩写和模糊命名？

### 常见错误避免
- ❌ 混合不同的命名风格（camelCase vs snake_case）
- ❌ 使用不描述性的通用名称（如 Manager, Helper, Utils）
- ❌ 忽略 MVI 组件的特定命名约定
- ❌ 不一致的前缀和后缀使用

遵循这些命名约定确保代码库的一致性、可读性和可维护性，同时与 GymBro 项目的整体架构标准保持一致。

        }
    }
    ```
